#!/usr/bin/env python3
"""
AkShare数据提供器
集成akshare获取螺纹钢期货实时数据
"""

import akshare as ak
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import time
import warnings
warnings.filterwarnings('ignore')

class AkShareDataProvider:
    """AkShare数据提供器"""
    
    def __init__(self):
        """初始化数据提供器"""
        self.symbol_map = {
            'rebar': 'RB',  # 螺纹钢主力合约
            'rebar_main': 'RB88',  # 螺纹钢主力连续
        }
        
        print("📊 AkShare数据提供器初始化完成")
        print("   支持品种: 螺纹钢期货 (RB)")

    def test_connection(self):
        """测试AkShare连接"""
        try:
            # 尝试获取一条简单数据来测试连接
            test_data = ak.futures_main_sina(symbol="RB0", start_date="2025-01-01", end_date="2025-01-02")
            return test_data is not None
        except Exception as e:
            print(f"⚠️ AkShare连接测试失败: {str(e)}")
            return False

    def get_futures_realtime_data(self, symbol='rebar'):
        """获取期货实时数据（优先1分钟K线）"""
        try:
            print(f"📡 获取{symbol}实时数据...")

            # 方法1: 获取1分钟K线数据（最优选择）
            try:
                minute_data = self.get_futures_minute_data(symbol, period='1', count=1)
                if minute_data is not None and len(minute_data) > 0:
                    current_price = float(minute_data['Close'].iloc[-1])
                    timestamp = minute_data['Date'].iloc[-1] if 'Date' in minute_data.columns else datetime.now()
                    print(f"   ✅ 1分钟K线价格: {current_price:.0f} 元/吨")
                    return {
                        'symbol': symbol,
                        'current_price': current_price,
                        'timestamp': timestamp,
                        'source': '1min_kline'
                    }
            except Exception as e:
                print(f"   ⚠️ 1分钟K线获取失败: {str(e)}")

            # 方法2: 获取主力合约实时数据
            try:
                ak_symbol = self.symbol_map.get(symbol, 'RB')
                realtime_data = ak.futures_main_sina(symbol=ak_symbol)
                if not realtime_data.empty:
                    current_price = float(realtime_data['current_price'].iloc[0])
                    print(f"   ✅ 实时价格: {current_price:.0f} 元/吨")
                    return {
                        'symbol': symbol,
                        'current_price': current_price,
                        'timestamp': datetime.now(),
                        'source': 'sina_realtime'
                    }
            except Exception as e:
                print(f"   ⚠️ 实时数据获取失败: {str(e)}")

            # 方法3: 获取最新日K数据作为备选
            try:
                daily_data = self.get_futures_daily_data(symbol, days=1)
                if not daily_data.empty:
                    latest_price = daily_data['Close'].iloc[-1]
                    print(f"   ✅ 最新收盘价: {latest_price:.0f} 元/吨")
                    return {
                        'symbol': symbol,
                        'current_price': latest_price,
                        'timestamp': daily_data['Date'].iloc[-1],
                        'source': 'daily_backup'
                    }
            except Exception as e:
                print(f"   ⚠️ 日K备选数据获取失败: {str(e)}")

            return None

        except Exception as e:
            print(f"❌ 实时数据获取失败: {str(e)}")
            return None

    def get_real_time_price(self, symbol='rebar'):
        """获取实时价格（基于1分钟K线）"""
        try:
            # 优先获取1分钟K线数据
            minute_data = self.get_futures_minute_data(symbol, period='1', count=1)
            if minute_data is not None and len(minute_data) > 0:
                latest_price = minute_data['Close'].iloc[-1]
                print(f"   ✅ 1分钟K线价格: {latest_price:.0f} 元/吨")
                return latest_price

            # 备选：获取实时数据
            realtime_data = self.get_futures_realtime_data(symbol)
            if realtime_data:
                return realtime_data['current_price']
            return None
        except Exception as e:
            print(f"❌ 实时价格获取失败: {str(e)}")
            return None

    def get_futures_minute_data(self, symbol='rebar', period='1', count=60):
        """
        获取期货分钟K线数据

        Args:
            symbol: 期货品种
            period: K线周期 ('1', '5', '15', '30', '60')
            count: 获取数据条数
        """
        try:
            print(f"📊 获取{symbol} {period}分钟K线数据...")

            # 获取主力合约代码
            ak_symbol = self.symbol_map.get(symbol, 'RB')

            # 先获取主力合约具体代码
            try:
                # 获取当前主力合约
                main_contract = ak.futures_main_sina(symbol=ak_symbol)
                if not main_contract.empty:
                    # 提取主力合约代码，例如 RB2501
                    contract_code = main_contract.index[0] if hasattr(main_contract, 'index') else f"{ak_symbol}0"
                else:
                    contract_code = f"{ak_symbol}0"
            except:
                contract_code = f"{ak_symbol}0"

            print(f"   使用合约代码: {contract_code}")

            # 获取分钟K线数据
            minute_data = ak.futures_zh_minute_sina(symbol=contract_code, period=period)

            if minute_data is not None and len(minute_data) > 0:
                # 只取最新的count条数据
                if len(minute_data) > count:
                    minute_data = minute_data.tail(count)

                # 重命名列以保持一致性
                if 'datetime' in minute_data.columns:
                    minute_data = minute_data.rename(columns={'datetime': 'Date'})
                elif 'time' in minute_data.columns:
                    minute_data = minute_data.rename(columns={'time': 'Date'})

                # 确保有必要的列
                required_columns = ['Open', 'High', 'Low', 'Close', 'Volume']
                for col in required_columns:
                    if col.lower() in minute_data.columns:
                        minute_data[col] = minute_data[col.lower()]

                print(f"   ✅ 获取{period}分钟K线: {len(minute_data)} 条记录")
                return minute_data
            else:
                print(f"   ⚠️ {period}分钟K线数据为空")
                return None

        except Exception as e:
            print(f"   ❌ {period}分钟K线获取失败: {str(e)}")
            return None

    def get_futures_60min_historical_data(self, symbol='rebar', days=730):
        """获取期货60分钟历史数据"""
        try:
            print(f"📊 获取{symbol} 60分钟历史数据...")

            # 获取60分钟数据
            data_60min = ak.futures_zh_minute_sina(symbol="RB0", period="60")

            if data_60min is not None and len(data_60min) > 0:
                # 数据预处理
                data_60min = data_60min.reset_index()

                # 重命名列
                if 'datetime' in data_60min.columns:
                    data_60min = data_60min.rename(columns={'datetime': 'Date'})
                elif 'time' in data_60min.columns:
                    data_60min = data_60min.rename(columns={'time': 'Date'})

                # 确保列名正确
                column_mapping = {
                    'open': 'Open',
                    'high': 'High',
                    'low': 'Low',
                    'close': 'Close',
                    'volume': 'Volume'
                }

                for old_col, new_col in column_mapping.items():
                    if old_col in data_60min.columns:
                        data_60min[new_col] = data_60min[old_col]

                # 确保有必要的列
                required_columns = ['Date', 'Open', 'High', 'Low', 'Close', 'Volume']
                available_columns = [col for col in required_columns if col in data_60min.columns]
                data_60min = data_60min[available_columns]

                # 数据清理
                data_60min = data_60min.dropna()

                # 确保Date列是datetime类型
                if 'Date' in data_60min.columns:
                    data_60min['Date'] = pd.to_datetime(data_60min['Date'])

                # 按时间排序
                data_60min = data_60min.sort_values('Date').reset_index(drop=True)

                # 过滤指定天数的数据
                if days > 0:
                    end_date = data_60min['Date'].max()
                    start_date = end_date - timedelta(days=days)
                    data_60min = data_60min[data_60min['Date'] >= start_date].reset_index(drop=True)

                print(f"✅ 获取60分钟数据: {len(data_60min)} 条记录")
                print(f"📈 时间范围: {data_60min['Date'].min()} 至 {data_60min['Date'].max()}")

                # 保存数据
                output_file = 'data/rebar_60min_data.csv'
                data_60min.to_csv(output_file, index=False)
                print(f"💾 60分钟数据已保存: {output_file}")

                return data_60min
            else:
                print("❌ 未获取到60分钟数据")
                return None

        except Exception as e:
            print(f"❌ 60分钟数据获取失败: {str(e)}")
            return None

    def get_futures_daily_data(self, symbol='rebar', days=1000, end_date=None):
        """获取期货日K数据"""
        try:
            if end_date is None:
                end_date = datetime.now().strftime('%Y%m%d')
            elif isinstance(end_date, datetime):
                end_date = end_date.strftime('%Y%m%d')
            
            # 计算开始日期
            start_date = (datetime.strptime(end_date, '%Y%m%d') - timedelta(days=days*2)).strftime('%Y%m%d')
            
            print(f"📊 获取{symbol}历史数据 ({start_date} - {end_date})")
            
            # 获取螺纹钢期货主力连续数据
            ak_symbol = self.symbol_map.get(symbol, 'RB')
            
            # 尝试不同的数据源
            data = None
            
            # 方法1: 期货主力连续数据
            try:
                print("   尝试获取主力连续数据...")
                data = ak.futures_zh_daily_sina(symbol=f"{ak_symbol}0")  # 主力连续
                if data is not None and not data.empty:
                    print(f"   ✅ 主力连续数据: {len(data)} 条记录")
                else:
                    raise Exception("主力连续数据为空")
            except Exception as e:
                print(f"   ⚠️ 主力连续数据获取失败: {str(e)}")
            
            # 方法2: 期货历史数据
            if data is None or data.empty:
                try:
                    print("   尝试获取期货历史数据...")
                    data = ak.futures_main_sina(symbol=ak_symbol)
                    if data is not None and not data.empty:
                        print(f"   ✅ 期货历史数据: {len(data)} 条记录")
                    else:
                        raise Exception("期货历史数据为空")
                except Exception as e:
                    print(f"   ⚠️ 期货历史数据获取失败: {str(e)}")
            
            # 方法3: 使用备选数据源
            if data is None or data.empty:
                try:
                    print("   尝试获取备选数据源...")
                    # 这里可以添加其他数据源
                    data = self.get_backup_data(symbol, start_date, end_date)
                except Exception as e:
                    print(f"   ⚠️ 备选数据源获取失败: {str(e)}")
            
            if data is None or data.empty:
                print("❌ 所有数据源均获取失败")
                return pd.DataFrame()
            
            # 数据预处理
            data = self.preprocess_futures_data(data)
            
            # 筛选日期范围
            if 'Date' in data.columns:
                data = data[data['Date'] <= pd.to_datetime(end_date)]
                data = data.tail(days)  # 取最近的数据
            
            print(f"   ✅ 数据预处理完成: {len(data)} 条记录")
            return data
            
        except Exception as e:
            print(f"❌ 历史数据获取失败: {str(e)}")
            return pd.DataFrame()
    
    def get_backup_data(self, symbol, start_date, end_date):
        """获取备选数据源"""
        try:
            # 如果akshare获取失败，使用本地备份数据
            backup_file = 'data/real_rebar_20250729.csv'
            if pd.io.common.file_exists(backup_file):
                print(f"   使用本地备份数据: {backup_file}")
                data = pd.read_csv(backup_file)
                data['Date'] = pd.to_datetime(data['Date'])
                return data
            else:
                return pd.DataFrame()
        except Exception as e:
            print(f"   备选数据源失败: {str(e)}")
            return pd.DataFrame()
    
    def preprocess_futures_data(self, data):
        """预处理期货数据"""
        try:
            # 标准化列名
            column_mapping = {
                'date': 'Date',
                'open': 'Open',
                'high': 'High',
                'low': 'Low',
                'close': 'Close',
                'volume': 'Volume',
                'hold': 'OpenInterest',
                '日期': 'Date',
                '开盘': 'Open',
                '最高': 'High',
                '最低': 'Low',
                '收盘': 'Close',
                '成交量': 'Volume',
                '持仓量': 'OpenInterest'
            }
            
            # 重命名列
            for old_name, new_name in column_mapping.items():
                if old_name in data.columns:
                    data = data.rename(columns={old_name: new_name})
            
            # 确保必要的列存在
            required_columns = ['Date', 'Open', 'High', 'Low', 'Close']
            for col in required_columns:
                if col not in data.columns:
                    if col == 'Date' and data.index.name == 'date':
                        data = data.reset_index()
                        data = data.rename(columns={'date': 'Date'})
                    elif col == 'Date':
                        # 如果没有日期列，使用索引
                        data['Date'] = pd.date_range(end=datetime.now(), periods=len(data), freq='D')
            
            # 转换数据类型
            if 'Date' in data.columns:
                data['Date'] = pd.to_datetime(data['Date'])
            
            for col in ['Open', 'High', 'Low', 'Close']:
                if col in data.columns:
                    data[col] = pd.to_numeric(data[col], errors='coerce')
            
            # 删除无效数据
            data = data.dropna(subset=['Close'])
            
            # 按日期排序
            if 'Date' in data.columns:
                data = data.sort_values('Date').reset_index(drop=True)
            
            return data
            
        except Exception as e:
            print(f"❌ 数据预处理失败: {str(e)}")
            return data
    
    def get_latest_data_for_strategy(self, symbol='rebar', days=1000):
        """获取策略所需的最新数据"""
        try:
            print(f"🔄 获取策略所需的最新数据...")
            
            # 获取历史数据
            historical_data = self.get_futures_daily_data(symbol, days)
            
            if historical_data.empty:
                print("❌ 无法获取历史数据")
                return None
            
            # 获取实时数据
            realtime_info = self.get_futures_realtime_data(symbol)
            
            # 如果有实时数据，更新最新一条记录
            if realtime_info and 'current_price' in realtime_info:
                current_price = realtime_info['current_price']
                
                # 更新最新一天的收盘价
                if len(historical_data) > 0:
                    latest_date = historical_data['Date'].iloc[-1].date()
                    today = datetime.now().date()
                    
                    if latest_date == today:
                        # 更新今天的数据
                        historical_data.loc[historical_data.index[-1], 'Close'] = current_price
                        print(f"   ✅ 更新今日收盘价: {current_price:.0f}")
                    else:
                        # 添加新的一天数据
                        new_row = historical_data.iloc[-1].copy()
                        new_row['Date'] = datetime.now()
                        new_row['Open'] = current_price
                        new_row['High'] = current_price
                        new_row['Low'] = current_price
                        new_row['Close'] = current_price
                        
                        historical_data = pd.concat([historical_data, new_row.to_frame().T], ignore_index=True)
                        print(f"   ✅ 添加今日数据: {current_price:.0f}")
            
            print(f"   ✅ 数据准备完成: {len(historical_data)} 条记录")
            print(f"   📅 数据范围: {historical_data['Date'].min().strftime('%Y-%m-%d')} 至 {historical_data['Date'].max().strftime('%Y-%m-%d')}")
            print(f"   💰 最新价格: {historical_data['Close'].iloc[-1]:.0f} 元/吨")
            
            return historical_data
            
        except Exception as e:
            print(f"❌ 获取策略数据失败: {str(e)}")
            return None
    
    def test_data_connection(self):
        """测试数据连接"""
        print("🧪 测试AkShare数据连接...")
        
        try:
            # 测试实时数据
            print("\n1. 测试实时数据获取:")
            realtime_data = self.get_futures_realtime_data('rebar')
            if realtime_data:
                print(f"   ✅ 实时数据获取成功")
                print(f"   💰 当前价格: {realtime_data['current_price']:.0f} 元/吨")
                print(f"   🕐 时间戳: {realtime_data['timestamp']}")
            else:
                print("   ❌ 实时数据获取失败")
            
            # 测试历史数据
            print("\n2. 测试历史数据获取:")
            historical_data = self.get_futures_daily_data('rebar', days=10)
            if not historical_data.empty:
                print(f"   ✅ 历史数据获取成功: {len(historical_data)} 条记录")
                print(f"   📅 最新日期: {historical_data['Date'].max()}")
                print(f"   💰 最新收盘: {historical_data['Close'].iloc[-1]:.0f} 元/吨")
            else:
                print("   ❌ 历史数据获取失败")
            
            # 测试策略数据
            print("\n3. 测试策略数据整合:")
            strategy_data = self.get_latest_data_for_strategy('rebar', days=100)
            if strategy_data is not None:
                print(f"   ✅ 策略数据整合成功: {len(strategy_data)} 条记录")
                return True
            else:
                print("   ❌ 策略数据整合失败")
                return False
                
        except Exception as e:
            print(f"❌ 数据连接测试失败: {str(e)}")
            return False


def main():
    """测试数据提供器"""
    print("🚀 AkShare数据提供器测试")
    print("=" * 50)
    
    # 初始化数据提供器
    provider = AkShareDataProvider()
    
    # 测试数据连接
    success = provider.test_data_connection()
    
    if success:
        print(f"\n✅ AkShare数据提供器测试成功!")
        print(f"💡 可以集成到AI策略系统中使用")
    else:
        print(f"\n❌ AkShare数据提供器测试失败!")
        print(f"💡 请检查网络连接和akshare版本")


if __name__ == "__main__":
    main()
