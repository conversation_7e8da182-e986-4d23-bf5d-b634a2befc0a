#!/usr/bin/env python3
"""
测试1分钟K线数据获取
验证新的实时数据获取方式
"""

import sys
sys.path.append('src')

import time
from datetime import datetime
from akshare_data_provider import AkShareDataProvider

def test_1min_data():
    """测试1分钟K线数据获取"""
    print("📊 1分钟K线数据获取测试")
    print("=" * 50)
    
    try:
        # 初始化数据提供器
        provider = AkShareDataProvider()
        
        # 1. 测试1分钟K线数据获取
        print("\n1️⃣ 1分钟K线数据测试")
        print("-" * 30)
        
        minute_data = provider.get_futures_minute_data('rebar', period='1', count=5)
        
        if minute_data is not None and len(minute_data) > 0:
            print(f"   ✅ 1分钟K线获取成功")
            print(f"   📊 数据条数: {len(minute_data)} 条")
            print(f"   📋 数据列: {list(minute_data.columns)}")
            
            # 显示最新几条数据
            print("\n   📈 最新1分钟K线数据:")
            for i, (idx, row) in enumerate(minute_data.tail(3).iterrows()):
                date_col = 'Date' if 'Date' in row else ('datetime' if 'datetime' in row else 'time')
                if date_col in row:
                    time_str = str(row[date_col])
                else:
                    time_str = f"第{i+1}条"
                
                print(f"   {time_str}: 开{row['Open']:.0f} 高{row['High']:.0f} 低{row['Low']:.0f} 收{row['Close']:.0f}")
        else:
            print("   ❌ 1分钟K线获取失败")
        
        # 2. 测试实时价格获取（基于1分钟K线）
        print("\n2️⃣ 实时价格获取测试")
        print("-" * 30)
        
        print("   正在获取实时价格...")
        realtime_data = provider.get_futures_realtime_data()
        
        if realtime_data:
            print(f"   ✅ 实时数据获取成功")
            print(f"   📊 数据源: {realtime_data['source']}")
            print(f"   💰 当前价格: {realtime_data['current_price']:.0f} 元/吨")
            print(f"   ⏰ 时间戳: {realtime_data['timestamp']}")
        else:
            print("   ❌ 实时数据获取失败")
        
        # 3. 测试简化的实时价格接口
        print("\n3️⃣ 简化实时价格接口测试")
        print("-" * 30)
        
        prices = []
        for i in range(3):
            price = provider.get_real_time_price()
            if price:
                prices.append((datetime.now(), price))
                print(f"   第{i+1}次: {price:.0f} 元/吨 @ {datetime.now().strftime('%H:%M:%S')}")
            else:
                print(f"   第{i+1}次: 获取失败")
            
            if i < 2:  # 不在最后一次等待
                time.sleep(2)
        
        # 4. 数据质量分析
        print("\n4️⃣ 数据质量分析")
        print("-" * 30)
        
        if len(prices) > 1:
            price_changes = [abs(prices[i][1] - prices[i-1][1]) for i in range(1, len(prices))]
            avg_change = sum(price_changes) / len(price_changes)
            max_change = max(price_changes)
            
            print(f"   📊 平均价格变化: {avg_change:.2f} 元/吨")
            print(f"   📊 最大价格变化: {max_change:.2f} 元/吨")
            
            if avg_change == 0:
                print("   📊 价格稳定性: 完全稳定（可能是非交易时间）")
            elif avg_change < 5:
                print("   📊 价格稳定性: 较稳定")
            elif avg_change < 20:
                print("   📊 价格稳定性: 正常波动")
            else:
                print("   📊 价格稳定性: 波动较大")
        
        # 5. 不同周期K线对比
        print("\n5️⃣ 不同周期K线对比")
        print("-" * 30)
        
        periods = ['1', '5', '15']
        for period in periods:
            try:
                data = provider.get_futures_minute_data('rebar', period=period, count=1)
                if data is not None and len(data) > 0:
                    latest_price = data['Close'].iloc[-1]
                    print(f"   {period}分钟K线: {latest_price:.0f} 元/吨")
                else:
                    print(f"   {period}分钟K线: 获取失败")
            except Exception as e:
                print(f"   {period}分钟K线: 错误 - {str(e)}")
        
        # 6. 数据时效性评估
        print("\n6️⃣ 数据时效性评估")
        print("-" * 30)
        
        current_time = datetime.now()
        
        if realtime_data and 'timestamp' in realtime_data:
            data_time = realtime_data['timestamp']
            if isinstance(data_time, str):
                try:
                    data_time = datetime.strptime(data_time, '%Y-%m-%d %H:%M:%S')
                except:
                    try:
                        data_time = datetime.strptime(data_time, '%Y-%m-%d')
                    except:
                        data_time = current_time
            
            time_diff = (current_time - data_time).total_seconds()
            
            print(f"   📊 数据时间: {data_time}")
            print(f"   📊 当前时间: {current_time}")
            print(f"   📊 时间差: {time_diff:.0f} 秒")
            
            if time_diff < 60:
                print("   ✅ 时效性: 优秀（1分钟内）")
            elif time_diff < 300:
                print("   ✅ 时效性: 良好（5分钟内）")
            elif time_diff < 3600:
                print("   ⚠️ 时效性: 一般（1小时内）")
            else:
                print("   ❌ 时效性: 较差（超过1小时）")
        
        # 7. 实盘交易适用性评估
        print("\n7️⃣ 实盘交易适用性评估")
        print("-" * 30)
        
        score = 0
        
        if minute_data is not None and len(minute_data) > 0:
            score += 40
            print("   ✅ 1分钟K线: 可获取 (+40分)")
        else:
            print("   ❌ 1分钟K线: 无法获取 (+0分)")
        
        if realtime_data:
            score += 30
            print("   ✅ 实时数据: 可获取 (+30分)")
        else:
            print("   ❌ 实时数据: 无法获取 (+0分)")
        
        if len(prices) > 0 and all(p[1] is not None for p in prices):
            score += 20
            print("   ✅ 价格稳定性: 良好 (+20分)")
        else:
            print("   ❌ 价格稳定性: 较差 (+0分)")
        
        if realtime_data and time_diff < 300:
            score += 10
            print("   ✅ 数据时效性: 良好 (+10分)")
        else:
            print("   ⚠️ 数据时效性: 一般 (+5分)")
            score += 5
        
        print(f"\n   📊 实盘适用性评分: {score}/100")
        
        if score >= 80:
            print("   🎉 评估结果: 完全适合实盘交易")
        elif score >= 60:
            print("   ✅ 评估结果: 基本适合实盘交易")
        elif score >= 40:
            print("   ⚠️ 评估结果: 谨慎使用于实盘")
        else:
            print("   ❌ 评估结果: 不建议用于实盘")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🔍 1分钟K线数据获取测试")
    print("🎯 目标: 验证1分钟K线作为实时数据的可行性")
    print()
    
    test_1min_data()
    
    print("\n" + "=" * 50)
    print("📋 测试完成")
    print("💡 1分钟K线数据比日K数据更适合实时交易")

if __name__ == "__main__":
    main()
