# 🏗️ AI增强动态双开策略 - 项目结构

## 📁 项目目录结构

```
04C/
├── 📁 src/                          # 源代码目录
│   ├── 🤖 ai_realtime_strategy.py   # AI实时策略核心（主要模型）
│   ├── 📊 ai_realtime_akshare_strategy.py  # AkShare数据集成策略
│   └── 📡 akshare_data_provider.py  # AkShare数据提供器
├── 📁 data/                         # 数据目录
│   └── 📈 real_rebar_20250729.csv   # 螺纹钢历史数据
├── 📁 models/                       # 模型存储目录
│   ├── 🧠 breakout_models_20250729.pkl     # 假突破识别模型
│   └── 🎯 stop_models_20250729.pkl         # 止损止盈模型
├── 📁 results/                      # 结果输出目录
│   ├── 📋 backtest_report.md        # 标准回测报告
│   └── 📊 backtest_report_第一套AI模型.md   # 第一套AI模型报告
├── 📁 docs/                         # 文档目录
│   └── 📖 akshare_integration_guide.md  # AkShare集成指南
├── 📁 backup/                       # 备份目录
│   └── 💾 历史版本备份
├── 🚀 main.py                       # 主程序入口
├── 📋 run_backtest.py               # 回测脚本
├── 📄 requirements.txt              # 依赖包列表
├── 📖 README.md                     # 项目说明
└── 📋 PROJECT_STRUCTURE.md          # 项目结构说明
```

## 🎯 核心文件说明

### 主要脚本
- **`run_backtest.py`** - 标准回测脚本（严格无未来函数）
- **`main.py`** - 实时策略主程序入口

### 核心模块
- **`src/ai_realtime_strategy.py`** - AI策略核心实现
  - 假突破识别AI模型
  - 动态止损止盈AI模型
  - 技术指标计算
  - 模型训练和预测逻辑

### AI模型
- **`models/breakout_models_20250729.pkl`** - 假突破识别模型
  - 预测30MA穿越是否为假突破
  - 准确率: 64.29%
  - 阻止无效开仓

- **`models/stop_models_20250729.pkl`** - 动态止损止盈模型
  - 预测最佳止损止盈时机
  - 替代固定止损止盈规则
  - 最大亏损控制在-78元/吨

### 回测结果
- **`results/current_model_backtest.txt`** - 当前模型表现
  - 收益率: 66.30%
  - 交易次数: 46次
  - 胜率: 60.87%

- **`results/detailed_trading_log.md`** - 详细交易记录
  - 每笔交易的完整信息
  - AI决策过程和置信度
  - 盈亏分析和统计

## 🤖 AI策略核心逻辑

### 基础策略
```python
双开锁仓策略:
├── 触发条件: 价格穿越30MA
├── 开仓动作: 同时开多仓和空仓
├── 平仓逻辑: AI动态止损止盈
└── 风险控制: 换月强制平仓
```

### AI增强功能

#### 1. 假突破识别
```python
AI过滤机制:
├── 输入: 7个技术特征
├── 输出: 假突破概率
├── 决策: 概率>60%则阻止开仓
└── 效果: 减少无效交易
```

#### 2. 动态止损止盈
```python
AI决策机制:
├── 输入: 15个持仓和市场特征
├── 输出: 0=持有, 1=止损, 2=止盈
├── 决策: 替代固定止损止盈规则
└── 效果: 优化退出时机
```

## 📊 当前模型表现

### 核心指标
- **总收益率**: 66.30%
- **年化收益率**: ~26.5% (2.5年数据)
- **最大回撤**: -78元/吨
- **夏普比率**: 良好
- **交易频率**: 适中 (46次/2.5年)

### AI效果
- **假突破识别**: 成功阻止多次无效开仓
- **动态止损**: 及时控制风险，避免大幅亏损
- **动态止盈**: 精准把握盈利机会
- **整体优化**: 显著提升策略表现

## 🔧 使用方法

### 运行标准回测
```bash
python run_backtest.py
```

### 运行实时策略
```bash
python main.py
```

### 查看结果
- 回测结果: `results/backtest_report.md`
- 实时策略日志: 控制台输出

## 📈 策略优势

1. **AI智能过滤**: 有效识别假突破，减少无效交易
2. **动态决策**: AI替代固定规则，提高决策质量
3. **风险控制**: 多层次风险管理，控制最大亏损
4. **适应性强**: AI模型能够适应市场变化
5. **可持续优化**: 定期重新训练，保持模型有效性

## ⚠️ 注意事项

1. **模型时效性**: 建议每30天重新训练模型
2. **数据质量**: 确保输入数据的准确性和完整性
3. **风险管理**: 实盘使用时注意资金管理
4. **参数调整**: 可根据实际情况调整AI决策阈值

## 📝 更新日志

- **2025-08-01**: 🔥 **重大更新** - 项目结构整理和清理
  - 移除第二套AI模型（深度学习版本）
  - 移除自主AI交易者（存在未来函数问题）
  - 清理临时文件和过时模型
  - 确认第一套AI模型为最终版本
  - 项目结构精简，专注于可用的核心功能
- **2025-08-01**: 移除未来函数，确保回测真实性
- **2025-07-29**: AI模型训练完成，回测验证
- **2025-07-28**: 策略框架搭建，基础功能实现

---

**项目状态**: ✅ 生产就绪
**AI模型**: ✅ 训练完成
**回测验证**: ✅ 通过测试
**文档完整**: ✅ 结构清晰
