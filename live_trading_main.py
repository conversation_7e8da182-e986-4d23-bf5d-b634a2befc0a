#!/usr/bin/env python3
"""
实盘交易主程序
收盘后计算交易信号，与回测逻辑完全一致
"""

import sys
sys.path.append('src')

import pandas as pd
import numpy as np
from datetime import datetime, time, timedelta
import time as time_module
import schedule
from ai_realtime_strategy import AIRealtimeStrategy
from akshare_data_provider import AkShareDataProvider

class LiveTradingSystem:
    """实盘交易系统 - 收盘后计算模式"""

    def __init__(self):
        self.strategy = AIRealtimeStrategy()
        self.data_provider = AkShareDataProvider()

        # 加载AI模型
        if not self.strategy.load_latest_models():
            print("❌ AI模型加载失败")
            return

        self.last_signal = None
        self.last_update_time = None
        self.trading_log = []

        print("🚀 实盘交易系统初始化完成")
        print("📊 策略: 收盘后计算交易信号（与回测一致）")
        print("🤖 AI模型已加载，准备就绪")
    
    def daily_closing_analysis(self):
        """收盘后分析（15:30执行）"""
        print("\n🌆 收盘后分析开始...")

        try:
            # 1. 获取最新数据（包含今日收盘价）
            historical_data = self.data_provider.get_rebar_data(days=100)

            # 2. 计算技术指标
            data_with_indicators = self.strategy.calculate_technical_indicators(historical_data)

            # 3. 生成次日交易信号
            current_index = len(data_with_indicators) - 1
            signal = self.strategy.get_daily_closing_trading_signal(data_with_indicators, current_index)

            # 4. 保存交易信号
            self.last_signal = signal

            # 5. 生成交易计划报告
            self.generate_next_day_trading_plan(signal, data_with_indicators.iloc[-1])
            
            print("✅ 早盘准备完成，系统就绪")
            
        except Exception as e:
            print(f"❌ 早盘准备失败: {str(e)}")
    
    def generate_next_day_trading_plan(self, signal, latest_data):
        """生成次日交易计划"""
        print("\n📋 生成次日交易计划...")

        plan = []
        plan.append("# 次日交易计划")
        plan.append(f"## 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        plan.append("")
        plan.append("## 市场分析")
        plan.append(f"- **当前价格**: {latest_data['Close']:.0f} 元/吨")
        plan.append(f"- **MA30**: {latest_data['MA30']:.0f} 元/吨")
        plan.append(f"- **RSI**: {latest_data.get('RSI', 'N/A')}")
        plan.append("")
        plan.append("## 交易信号")
        plan.append(f"- **操作建议**: {signal['action']}")
        plan.append(f"- **信号原因**: {signal['reason']}")
        plan.append(f"- **信号强度**: {signal['confidence']:.1%}")
        plan.append("")

        if signal['action'] != 'hold':
            plan.append("## 执行计划")
            plan.append("### 开仓策略")
            plan.append("- **方式**: 双开锁仓")
            plan.append("- **时机**: 信号确认后立即执行")
            plan.append("- **仓位**: 1手多头 + 1手空头")
            plan.append("")
            plan.append("### 风险控制")
            plan.append("- **止损**: -30元/吨")
            plan.append("- **止盈**: +50元/吨")
            plan.append("- **AI决策**: 动态调整止损止盈")
            plan.append("")
        else:
            plan.append("## 执行计划")
            plan.append("- **操作**: 观望，无交易信号")
            plan.append("- **监控**: 继续关注价格走势")
            plan.append("")

        # 保存交易计划
        with open('results/next_day_trading_plan.md', 'w', encoding='utf-8') as f:
            f.write('\n'.join(plan))

        print("   📄 交易计划已保存: results/next_day_trading_plan.md")

    def is_trading_time(self, current_time):
        """检查是否在交易时间"""
        # 日盘: 09:00-11:30, 13:30-15:00
        # 夜盘: 21:00-23:00
        morning_start = time(9, 0)
        morning_end = time(11, 30)
        afternoon_start = time(13, 30)
        afternoon_end = time(15, 0)
        night_start = time(21, 0)
        night_end = time(23, 0)

        return (
            (morning_start <= current_time <= morning_end) or
            (afternoon_start <= current_time <= afternoon_end) or
            (night_start <= current_time <= night_end)
        )

    def real_time_monitoring(self):
        """实时监控（交易时间内每分钟执行）"""
        try:
            # 1. 获取实时价格
            current_price = self.data_provider.get_real_time_price()
            current_time = datetime.now()
            
            if current_price is None:
                print("⚠️ 无法获取实时价格")
                return
            
            print(f"📊 {current_time.strftime('%H:%M:%S')} 价格: {current_price:.0f}")
            
            # 2. 使用与回测一致的AI策略检查信号
            # 获取历史数据用于AI分析
            historical_data = self.data_provider.get_rebar_data(days=100)
            data_with_indicators = self.strategy.calculate_technical_indicators(historical_data)

            # 检查是否有MA30穿越信号（与回测逻辑一致）
            current_index = len(data_with_indicators) - 1
            if current_index > 30:
                current_ma30 = data_with_indicators.loc[current_index, 'MA30']
                prev_price = data_with_indicators.loc[current_index-1, 'Close']
                prev_ma30 = data_with_indicators.loc[current_index-1, 'MA30']

                # 检查穿越
                price_cross_ma30 = (
                    (prev_price > prev_ma30 and current_price <= current_ma30) or
                    (prev_price < prev_ma30 and current_price >= current_ma30)
                )

                if price_cross_ma30:
                    # 确定穿越方向
                    if prev_price <= prev_ma30 and current_price > current_ma30:
                        breakout_type = 'up'
                    else:
                        breakout_type = 'down'

                    # 使用与回测相同的AI假突破检测
                    ai_prob = self.strategy.predict_false_breakout(data_with_indicators, current_index, breakout_type)

                    if ai_prob <= 0.5:  # AI认为是真突破
                        signal = {
                            'action': 'buy' if breakout_type == 'up' else 'sell',
                            'price': current_price,
                            'ma30': current_ma30,
                            'ai_prob': ai_prob,
                            'reason': f'{breakout_type}穿MA30，AI确认真突破',
                            'timestamp': current_time
                        }
                        self.execute_trade_signal(signal, 'realtime')
                    else:
                        print(f"🛡️ AI识别假突破: {breakout_type}穿越，概率{ai_prob:.1%}")
                else:
                    print(f"📊 无穿越信号: 价格{current_price:.0f}, MA30{current_ma30:.0f}")
            
            # 4. 更新状态
            self.last_price = current_price
            self.last_update_time = current_time
            
            # 5. 更新状态
            self.last_price = current_price
            self.last_update_time = current_time
            
        except Exception as e:
            print(f"❌ 实时监控错误: {str(e)}")
    
    def execute_trade_signal(self, signal, source):
        """执行交易信号"""
        print(f"\n🚨 {source}策略信号触发:")
        print(f"   动作: {signal['action']}")
        print(f"   价格: {signal.get('price', 'N/A')}")
        print(f"   原因: {signal.get('reason', 'N/A')}")
        
        # 记录交易日志
        trade_log = {
            'timestamp': datetime.now(),
            'source': source,
            'signal': signal,
            'executed': False
        }
        
        # 这里应该连接实际的交易接口
        # 目前只是模拟执行
        try:
            if signal['action'] in ['buy', 'sell']:
                print("📝 模拟执行: 双开锁仓订单")
                print(f"   多头开仓: {signal['price']:.0f}")
                print(f"   空头开仓: {signal['price']:.0f}")
                trade_log['executed'] = True

            elif signal['action'] in ['close_long', 'close_short']:
                print("📝 模拟执行: 平仓订单")
                print(f"   平仓价格: {signal['price']:.0f}")
                trade_log['executed'] = True
                
            # 发送通知（可以是微信、邮件等）
            self.send_notification(signal, source)
            
        except Exception as e:
            print(f"❌ 交易执行失败: {str(e)}")
            trade_log['error'] = str(e)
        
        self.trading_log.append(trade_log)
    
    def send_notification(self, signal, source):
        """发送交易通知"""
        message = f"""
🤖 AI交易信号 ({source})
⏰ 时间: {datetime.now().strftime('%H:%M:%S')}
📊 动作: {signal['action']}
💰 价格: {signal.get('price', 'N/A')}
📝 原因: {signal.get('reason', 'N/A')}
        """
        
        # 这里可以集成微信、钉钉、邮件等通知方式
        print(f"📱 通知已发送: {message.strip()}")
    
    def system_health_check(self):
        """系统健康检查"""
        print("🔍 系统健康检查...")
        
        checks = {
            '数据连接': self.data_provider.test_connection(),
            'AI模型': hasattr(self.strategy, 'breakout_models') and self.strategy.breakout_models is not None,
            '交易时间': self.is_trading_time(datetime.now().time()),
        }
        
        for check_name, status in checks.items():
            status_icon = "✅" if status else "❌"
            print(f"   {status_icon} {check_name}: {'正常' if status else '异常'}")
    
    def generate_daily_summary(self):
        """生成每日交易总结"""
        print("📋 生成每日交易总结...")
        
        today = datetime.now().strftime('%Y-%m-%d')
        today_logs = [log for log in self.trading_log 
                     if log['timestamp'].strftime('%Y-%m-%d') == today]
        
        summary = []
        summary.append(f"# {today} 交易总结")
        summary.append("")
        summary.append(f"## 📊 交易统计")
        summary.append(f"- 信号总数: {len(today_logs)}")
        summary.append(f"- 执行成功: {sum(1 for log in today_logs if log['executed'])}")
        summary.append(f"- 执行失败: {sum(1 for log in today_logs if not log['executed'])}")
        summary.append("")
        
        if today_logs:
            summary.append("## 📝 交易明细")
            for i, log in enumerate(today_logs, 1):
                summary.append(f"### {i}. {log['timestamp'].strftime('%H:%M:%S')}")
                summary.append(f"- 来源: {log['source']}")
                summary.append(f"- 动作: {log['signal']['action']}")
                summary.append(f"- 状态: {'✅成功' if log['executed'] else '❌失败'}")
                summary.append("")
        
        # 保存总结
        filename = f"results/daily_summary_{today}.md"
        with open(filename, 'w', encoding='utf-8') as f:
            f.write('\n'.join(summary))
        
        print(f"📄 每日总结已保存: {filename}")
    
    def start_live_trading(self):
        """启动实盘交易"""
        print("🚀 启动实盘交易系统...")
        
        # 设置定时任务
        schedule.every().day.at("08:30").do(self.morning_preparation)
        schedule.every().day.at("16:00").do(self.evening_analysis)
        
        # 交易时间内每分钟监控
        schedule.every().minute.do(self.real_time_monitoring)
        
        print("⏰ 定时任务已设置:")
        print("   08:30 - 早盘准备")
        print("   16:00 - 收盘分析")
        print("   交易时间 - 每分钟监控")
        print()
        print("🔄 系统运行中... (Ctrl+C 停止)")
        
        try:
            while True:
                schedule.run_pending()
                time_module.sleep(1)
                
        except KeyboardInterrupt:
            print("\n🛑 系统停止")
            self.generate_daily_summary()

def main():
    """主函数"""
    print("🤖 AI实盘交易系统")
    print("=" * 50)
    
    # 选择运行模式
    print("请选择运行模式:")
    print("1 - 完整实盘交易模式")
    print("2 - 仅生成交易计划")
    print("3 - 系统测试模式")
    
    choice = input("请选择 (1/2/3): ").strip()
    
    system = LiveTradingSystem()
    
    if choice == '1':
        system.start_live_trading()
    elif choice == '2':
        system.evening_analysis()
    elif choice == '3':
        system.system_health_check()
        system.real_time_monitoring()
    else:
        print("❌ 无效选择")

if __name__ == "__main__":
    main()
