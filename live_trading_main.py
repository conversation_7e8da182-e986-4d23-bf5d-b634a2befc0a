#!/usr/bin/env python3
"""
实盘交易主程序
收盘后计算交易信号，与回测逻辑完全一致
"""

import sys
sys.path.append('src')

import pandas as pd
import numpy as np
from datetime import datetime, time, timedelta
import time as time_module
import schedule
from ai_realtime_strategy import AIRealtimeStrategy
from akshare_data_provider import AkShareDataProvider

class LiveTradingSystem:
    """60分钟级别模拟交易系统"""

    def __init__(self):
        self.strategy = AIRealtimeStrategy()
        self.data_provider = AkShareDataProvider()

        # 加载AI模型
        if not self.strategy.load_latest_models():
            print("❌ AI模型加载失败")
            return

        # 模拟交易状态
        self.balance = 100000  # 初始资金10万
        self.long_position = 0  # 多头持仓（手数）
        self.short_position = 0  # 空头持仓（手数）
        self.long_entry_price = 0
        self.short_entry_price = 0
        self.long_entry_time = None
        self.short_entry_time = None

        self.last_signal = None
        self.last_update_time = None
        self.trading_log = []

        print("🚀 60分钟级别模拟交易系统初始化完成")
        print("📊 策略: 60分钟数据 + AI智能决策")
        print("💰 初始资金: 100,000 元")
        print("📋 交易规则: 双开各1手，允许查看后1-3小时做决策")
        print("🤖 AI模型已加载，准备就绪")
    
    def hourly_analysis(self):
        """每小时分析（60分钟级别）"""
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        print(f"\n⏰ {current_time} - 60分钟级别分析开始...")

        try:
            # 1. 获取最新60分钟数据
            historical_data = self.data_provider.get_futures_60min_historical_data(days=100)

            if historical_data is None or len(historical_data) < 50:
                print("❌ 60分钟数据获取失败或数据不足")
                return

            # 2. 计算技术指标
            data_with_indicators = self.strategy.calculate_technical_indicators(historical_data)

            # 3. 获取当前价格和分析
            current_index = len(data_with_indicators) - 1
            current_price = data_with_indicators.iloc[current_index]['Close']

            print(f"📊 当前价格: {current_price:.0f} 元/吨")
            print(f"💰 账户余额: {self.balance:,.0f} 元")
            print(f"📈 持仓状态: 多头{self.long_position}手, 空头{self.short_position}手")

            # 4. 检查止损止盈（如果有持仓）
            if self.long_position > 0 or self.short_position > 0:
                self.check_stop_loss_take_profit(data_with_indicators, current_index, current_price)

            # 5. 检查开仓信号（如果无持仓）
            if self.long_position == 0 and self.short_position == 0:
                self.check_entry_signals(data_with_indicators, current_index, current_price)

        except Exception as e:
            print(f"❌ 60分钟分析失败: {str(e)}")
            import traceback
            traceback.print_exc()

    def check_stop_loss_take_profit(self, data, current_index, current_price):
        """检查止损止盈（60分钟级别）"""

        # 检查多头持仓
        if self.long_position > 0:
            ai_action = self.strategy.determine_best_decision(
                data, current_index, self.long_entry_price, 'long', timeframe='60min'
            )

            if ai_action == 1:  # 止损
                profit = current_price - self.long_entry_price
                self.balance += profit * 10  # 1手=10吨
                self.trading_log.append({
                    'time': datetime.now(),
                    'action': '多头止损',
                    'price': current_price,
                    'profit': profit,
                    'balance': self.balance
                })
                print(f"🔴 多头止损: {self.long_entry_price:.0f}→{current_price:.0f}, {profit:+.0f}元/吨")
                self.long_position = 0

            elif ai_action == 2:  # 止盈
                profit = current_price - self.long_entry_price
                self.balance += profit * 10
                self.trading_log.append({
                    'time': datetime.now(),
                    'action': '多头止盈',
                    'price': current_price,
                    'profit': profit,
                    'balance': self.balance
                })
                print(f"🟢 多头止盈: {self.long_entry_price:.0f}→{current_price:.0f}, {profit:+.0f}元/吨")
                self.long_position = 0

        # 检查空头持仓
        if self.short_position > 0:
            ai_action = self.strategy.determine_best_decision(
                data, current_index, self.short_entry_price, 'short', timeframe='60min'
            )

            if ai_action == 1:  # 止损
                profit = self.short_entry_price - current_price
                self.balance += profit * 10
                self.trading_log.append({
                    'time': datetime.now(),
                    'action': '空头止损',
                    'price': current_price,
                    'profit': profit,
                    'balance': self.balance
                })
                print(f"🔴 空头止损: {self.short_entry_price:.0f}→{current_price:.0f}, {profit:+.0f}元/吨")
                self.short_position = 0

            elif ai_action == 2:  # 止盈
                profit = self.short_entry_price - current_price
                self.balance += profit * 10
                self.trading_log.append({
                    'time': datetime.now(),
                    'action': '空头止盈',
                    'price': current_price,
                    'profit': profit,
                    'balance': self.balance
                })
                print(f"🟢 空头止盈: {self.short_entry_price:.0f}→{current_price:.0f}, {profit:+.0f}元/吨")
                self.short_position = 0

    def check_entry_signals(self, data, current_index, current_price):
        """检查开仓信号（60分钟级别）"""

        if current_index < 50:  # 需要足够的历史数据
            return

        # 检查30MA穿越
        current_ma30 = data.iloc[current_index]['MA30']
        prev_price = data.iloc[current_index-1]['Close']
        prev_ma30 = data.iloc[current_index-1]['MA30']

        if pd.isna(current_ma30) or pd.isna(prev_ma30):
            return

        # 检测穿越
        price_cross_ma30 = (
            (prev_price > prev_ma30 and current_price <= current_ma30) or
            (prev_price < prev_ma30 and current_price >= current_ma30)
        )

        if price_cross_ma30:
            # 确定穿越方向
            if prev_price <= prev_ma30 and current_price > current_ma30:
                breakout_type = 'up'
                direction = "上穿"
            else:
                breakout_type = 'down'
                direction = "下穿"

            print(f"🔍 检测到30MA{direction}: {current_price:.0f} vs MA30({current_ma30:.0f})")

            # AI假突破识别
            ai_prob = self.strategy.predict_false_breakout(data, current_index, breakout_type)

            print(f"🤖 AI假突破概率: {ai_prob:.1%}")

            if ai_prob > 0.5:  # AI识别为假突破
                print(f"🛡️ AI阻止开仓: 假突破概率过高({ai_prob:.1%})")
                self.trading_log.append({
                    'time': datetime.now(),
                    'action': 'AI阻止开仓',
                    'price': current_price,
                    'reason': f'假突破概率{ai_prob:.1%}',
                    'balance': self.balance
                })
            else:
                # AI认为是真突破，执行双开
                print(f"🚀 AI确认真突破，执行双开锁仓")
                self.execute_double_open(current_price, direction)

    def execute_double_open(self, price, direction):
        """执行双开锁仓（各1手）"""

        # 双开锁仓
        self.long_position = 1
        self.short_position = 1
        self.long_entry_price = price
        self.short_entry_price = price
        self.long_entry_time = datetime.now()
        self.short_entry_time = datetime.now()

        # 记录交易
        self.trading_log.append({
            'time': datetime.now(),
            'action': f'双开锁仓({direction})',
            'price': price,
            'long_position': self.long_position,
            'short_position': self.short_position,
            'balance': self.balance
        })

        print(f"📈 多头开仓: 1手 @ {price:.0f} 元/吨")
        print(f"📉 空头开仓: 1手 @ {price:.0f} 元/吨")
        print(f"💰 当前余额: {self.balance:,.0f} 元")






    
    def start_60min_trading(self):
        """启动60分钟级别模拟交易"""
        print("🚀 启动60分钟级别模拟交易系统...")

        # 设置定时任务 - 每小时分析
        schedule.every().hour.at(":00").do(self.hourly_analysis)

        print("⏰ 定时任务已设置:")
        print("   每小时整点 - 60分钟级别分析")
        print("   模拟交易 - 双开各1手")
        print("   AI决策 - 允许查看后1-3小时")
        print()
        print("🔄 系统运行中... (Ctrl+C 停止)")
        print("💡 提示: 可以手动运行一次分析测试")

        # 立即运行一次分析
        print("\n🔄 立即执行一次分析...")
        self.hourly_analysis()

        try:
            while True:
                schedule.run_pending()
                time_module.sleep(60)  # 每分钟检查一次

        except KeyboardInterrupt:
            print("\n🛑 系统停止")
            self.print_trading_summary()

    def print_trading_summary(self):
        """打印交易总结"""
        print("\n📊 交易总结")
        print("=" * 50)
        print(f"💰 最终余额: {self.balance:,.0f} 元")
        print(f"📈 总收益: {self.balance - 100000:+,.0f} 元")
        print(f"📊 收益率: {(self.balance - 100000) / 100000:.2%}")
        print(f"📋 交易次数: {len(self.trading_log)} 次")

        if self.trading_log:
            print("\n📝 交易记录:")
            for i, log in enumerate(self.trading_log[-10:], 1):  # 显示最近10次
                print(f"{i}. {log['time'].strftime('%m-%d %H:%M')} - {log['action']} @ {log.get('price', 'N/A')}")

        print("\n💾 详细记录已保存到 trading_log")

def main():
    """主函数"""
    print("🤖 60分钟级别AI模拟交易系统")
    print("=" * 50)

    # 选择运行模式
    print("请选择运行模式:")
    print("1 - 60分钟级别自动交易")
    print("2 - 单次分析测试")
    print("3 - 查看交易状态")

    choice = input("请选择 (1/2/3): ").strip()

    system = LiveTradingSystem()

    if choice == '1':
        system.start_60min_trading()
    elif choice == '2':
        system.hourly_analysis()
    elif choice == '3':
        system.print_trading_summary()
    else:
        print("❌ 无效选择")

if __name__ == "__main__":
    main()
