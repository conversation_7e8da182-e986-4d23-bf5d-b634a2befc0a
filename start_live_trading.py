#!/usr/bin/env python3
"""
混合模式实盘交易系统启动脚本
整合盘中实时策略和预测式策略
"""

import sys
import os
from datetime import datetime

def print_banner():
    """打印系统横幅"""
    print("=" * 60)
    print("🤖 AI增强动态双开策略 - 实盘交易系统")
    print("=" * 60)
    print("📊 策略特点:")
    print("   ✅ 与回测一致的AI模型 - 确保结果可信")
    print("   ✅ MA30穿越信号检测 - 技术分析基础")
    print("   ✅ AI假突破识别 - 智能过滤信号")
    print("   ✅ 动态止损止盈 - AI决策风险控制")
    print("   ✅ 双开锁仓策略 - 降低方向性风险")
    print()
    print("🎯 年化收益率: 5.35% | 胜率: 56.52% | 风险可控")
    print("=" * 60)
    print()

def check_system_requirements():
    """检查系统要求"""
    print("🔍 检查系统要求...")
    
    requirements = {
        "Python版本": sys.version_info >= (3, 7),
        "数据文件": os.path.exists('data/real_rebar_20250729.csv'),
        "AI模型": os.path.exists('models/breakout_models_20250729.pkl') and 
                 os.path.exists('models/stop_models_20250729.pkl'),
        "源代码": os.path.exists('src/ai_realtime_strategy.py'),
        "结果目录": os.path.exists('results') or create_results_dir()
    }
    
    all_ok = True
    for name, status in requirements.items():
        icon = "✅" if status else "❌"
        print(f"   {icon} {name}")
        if not status:
            all_ok = False
    
    return all_ok

def create_results_dir():
    """创建结果目录"""
    try:
        os.makedirs('results', exist_ok=True)
        return True
    except:
        return False

def test_data_connection():
    """测试数据连接"""
    print("\n📡 测试数据连接...")
    
    try:
        sys.path.append('src')
        from akshare_data_provider import AkShareDataProvider
        
        provider = AkShareDataProvider()
        connection_ok = provider.test_connection()
        
        if connection_ok:
            print("   ✅ AkShare数据连接正常")
            return True
        else:
            print("   ❌ AkShare数据连接失败")
            return False
            
    except Exception as e:
        print(f"   ❌ 数据连接测试失败: {str(e)}")
        return False

def show_trading_schedule():
    """显示交易时间表"""
    print("\n⏰ 交易时间表:")
    print("   🌅 08:30 - 早盘准备（更新MA30基准，生成交易计划）")
    print("   📊 09:00-11:30 - 上午交易时间（实时监控）")
    print("   📊 13:30-15:00 - 下午交易时间（实时监控）")
    print("   🌆 16:00 - 收盘分析（生成明日交易计划）")
    print("   🌙 21:00-23:00 - 夜盘交易时间（实时监控）")
    print()

def show_strategy_details():
    """显示策略详情"""
    print("🎯 AI策略详情（与回测完全一致）:")
    print()
    print("📈 信号检测:")
    print("   • 基于MA30技术指标")
    print("   • 实时检测价格穿越MA30")
    print("   • 使用与回测相同的AI模型")
    print("   • AI假突破识别过滤")
    print()
    print("💼 交易执行:")
    print("   • 双开锁仓策略")
    print("   • 1手多头 + 1手空头")
    print("   • 降低方向性风险")
    print("   • 盈利来自价格波动")
    print()
    print("🛡️ 风险控制:")
    print("   • 多仓止损: -30元/吨")
    print("   • 多仓止盈: +50元/吨")
    print("   • 空仓止损: -30元/吨")
    print("   • 空仓止盈: +50元/吨")
    print("   • AI动态决策调整")
    print("   • 换月强制平仓")
    print()

def main():
    """主函数"""
    print_banner()
    
    # 系统检查
    if not check_system_requirements():
        print("\n❌ 系统要求检查失败，请解决上述问题后重试")
        return
    
    # 数据连接测试
    if not test_data_connection():
        print("\n⚠️ 数据连接异常，但系统仍可启动（将使用历史数据）")
    
    show_trading_schedule()
    show_strategy_details()
    
    # 选择运行模式
    print("🚀 请选择运行模式:")
    print("1 - 完整实盘交易模式（推荐）")
    print("2 - 仅生成今日交易计划")
    print("3 - 系统测试模式")
    print("4 - 查看策略详情")
    print("5 - 退出")
    print()
    
    while True:
        choice = input("请选择 (1-5): ").strip()
        
        if choice == '1':
            print("\n🚀 启动完整实盘交易模式...")
            print("⚠️ 注意: 这将启动自动交易系统")
            confirm = input("确认启动? (y/N): ").strip().lower()
            
            if confirm == 'y':
                print("\n🔄 正在启动混合模式实盘交易系统...")
                os.system('python live_trading_main.py')
            else:
                print("❌ 已取消启动")
            break
            
        elif choice == '2':
            print("\n📋 生成今日交易计划...")
            try:
                sys.path.append('src')
                from predictive_ai_strategy import PredictiveAIStrategy
                import pandas as pd
                
                strategy = PredictiveAIStrategy()
                historical_data = pd.read_csv('data/real_rebar_20250729.csv')
                strategy.generate_trading_plan_report(historical_data)
                print("✅ 交易计划生成完成")
            except Exception as e:
                print(f"❌ 生成失败: {str(e)}")
            break
            
        elif choice == '3':
            print("\n🧪 系统测试模式...")
            try:
                sys.path.append('src')
                from live_trading_main import LiveTradingSystem
                
                system = LiveTradingSystem()
                system.system_health_check()
                print("\n📊 模拟实时监控...")
                system.real_time_monitoring()
                print("✅ 系统测试完成")
            except Exception as e:
                print(f"❌ 测试失败: {str(e)}")
            break
            
        elif choice == '4':
            print("\n📖 查看策略详情...")
            show_strategy_details()
            print("按回车键继续...")
            input()
            continue
            
        elif choice == '5':
            print("👋 再见！")
            break
            
        else:
            print("❌ 无效选择，请重新输入")

if __name__ == "__main__":
    main()
