# AI交易模型使用说明

## 🎯 模型概述
- **模型名称**: AI增强动态双开策略
- **年化收益率**: 7.35%
- **总收益率**: 18.38% (2.5年)
- **胜率**: 60%
- **特点**: 无未来函数，实盘可复现

## 📁 文件结构
```
backup/
├── models/           # AI模型文件
├── src/             # 核心源代码
├── data/            # 历史数据
├── results/         # 回测结果
├── scripts/         # 运行脚本
├── model_info.json  # 模型信息
└── usage_guide.md   # 使用说明
```

## 🚀 快速开始

### 1. 环境准备
```bash
pip install pandas numpy scikit-learn matplotlib
```

### 2. 运行回测
```bash
python scripts/run_clean_backtest.py
```

### 3. 查看结果
- 交易记录: `results/corrected_trading_records.csv`
- 详细报告: `results/complete_trading_log_document.md`

## 🎯 策略参数
- **假突破阈值**: 0.5 (更严格过滤)
- **止损阈值**: -30元/吨
- **止盈阈值**: 20元/吨
- **MA周期**: 30日
- **RSI超买**: 70
- **RSI超卖**: 30

## 📊 核心表现
- **50笔交易**: 30笔盈利，20笔亏损
- **最大盈利**: 3,610元 (2024-09-30)
- **最大亏损**: -1,030元 (风险可控)
- **AI决策**: 92%为AI智能决策

## ⚠️ 重要提醒
1. **无未来函数**: 所有决策基于当前可获得数据
2. **实盘适用**: 100%符合实际交易要求
3. **风险控制**: 建议仓位不超过总资金30%
4. **定期维护**: 建议每月检查模型表现

## 🔧 模型训练
如需重新训练模型:
```bash
python scripts/train_models.py
```

## 📈 性能监控
定期运行以下脚本监控表现:
```bash
python scripts/generate_clean_trading_log.py
```

## 📞 技术支持
- 模型基于真实历史数据训练
- 经过严格的未来函数检查
- 适用于螺纹钢期货交易
- 可根据需要调整参数

---
**备份时间**: 2025-08-01 21:20:14
**模型版本**: 年化7.35%稳定版
**状态**: 生产就绪
