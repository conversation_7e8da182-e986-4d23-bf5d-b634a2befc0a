#!/usr/bin/env python3
"""
预测式AI交易策略
基于前一日数据预测次日可能的交易机会
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import pickle
from ai_realtime_strategy import AIRealtimeStrategy

class PredictiveAIStrategy(AIRealtimeStrategy):
    """预测式AI策略"""
    
    def __init__(self):
        super().__init__()
        self.next_day_signals = []
        self.price_targets = {}
        
        print("🔮 预测式AI策略初始化")
        print("📅 特点: 前一日预测，次日执行")
    
    def generate_next_day_signals(self, historical_data):
        """
        基于历史数据生成次日交易信号
        在前一日收盘后运行
        """
        print("🔮 生成次日交易信号...")
        
        if len(historical_data) < 100:
            print("❌ 历史数据不足")
            return []
        
        # 计算技术指标
        data = self.calculate_technical_indicators(historical_data)
        latest_data = data.iloc[-1]
        
        signals = []
        
        # 1. 预测MA30穿越概率
        ma30_cross_prob = self.predict_ma30_cross_probability(data)
        
        # 2. 计算关键价格位
        key_levels = self.calculate_key_price_levels(data)
        
        # 3. 生成条件订单信号
        if ma30_cross_prob['up'] > 0.3:  # 上穿概率大于30%
            signals.append({
                'type': 'conditional_buy',
                'trigger_price': key_levels['ma30_up_break'],
                'action': 'double_open',
                'probability': ma30_cross_prob['up'],
                'reason': f'预测上穿MA30概率{ma30_cross_prob["up"]:.1%}'
            })
        
        if ma30_cross_prob['down'] > 0.3:  # 下穿概率大于30%
            signals.append({
                'type': 'conditional_sell',
                'trigger_price': key_levels['ma30_down_break'],
                'action': 'double_open',
                'probability': ma30_cross_prob['down'],
                'reason': f'预测下穿MA30概率{ma30_cross_prob["down"]:.1%}'
            })
        
        # 4. 设置止损止盈价位
        stop_loss_levels = self.calculate_stop_loss_levels(latest_data['Close'])
        signals.extend(stop_loss_levels)
        
        self.next_day_signals = signals
        self.print_next_day_signals()
        
        return signals
    
    def predict_ma30_cross_probability(self, data):
        """预测MA30穿越概率"""
        latest = data.iloc[-1]
        current_price = latest['Close']
        current_ma30 = latest['MA30']
        
        # 计算价格相对MA30的位置
        price_distance = (current_price - current_ma30) / current_ma30
        
        # 计算近期波动率
        recent_volatility = data['Close'].tail(10).pct_change().std()
        
        # 简单的概率模型
        up_prob = 0.0
        down_prob = 0.0
        
        if price_distance > -0.02 and price_distance < 0.02:  # 接近MA30
            # 基于波动率估算穿越概率
            base_prob = min(recent_volatility * 20, 0.8)  # 最高80%
            
            if price_distance > 0:  # 在MA30上方
                down_prob = base_prob * 0.7  # 下穿概率
                up_prob = base_prob * 0.3    # 继续上涨概率
            else:  # 在MA30下方
                up_prob = base_prob * 0.7    # 上穿概率
                down_prob = base_prob * 0.3  # 继续下跌概率
        
        return {'up': up_prob, 'down': down_prob}
    
    def calculate_key_price_levels(self, data):
        """计算关键价格位"""
        latest = data.iloc[-1]
        current_ma30 = latest['MA30']
        
        # 预估次日MA30（简单移动）
        ma30_change = data['MA30'].diff().tail(5).mean()  # 近5日MA30变化均值
        tomorrow_ma30 = current_ma30 + ma30_change
        
        # 设置突破价位（考虑一定的缓冲）
        buffer = tomorrow_ma30 * 0.005  # 0.5%缓冲
        
        return {
            'ma30_up_break': tomorrow_ma30 + buffer,
            'ma30_down_break': tomorrow_ma30 - buffer,
            'tomorrow_ma30_est': tomorrow_ma30
        }
    
    def calculate_stop_loss_levels(self, current_price):
        """计算止损止盈价位"""
        levels = []
        
        # 多仓止损止盈
        levels.append({
            'type': 'stop_loss',
            'position': 'long',
            'trigger_price': current_price - 30,
            'action': 'close_long',
            'reason': '多仓止损-30元/吨'
        })
        
        levels.append({
            'type': 'take_profit',
            'position': 'long',
            'trigger_price': current_price + 50,
            'action': 'close_long',
            'reason': '多仓止盈+50元/吨'
        })
        
        # 空仓止损止盈
        levels.append({
            'type': 'stop_loss',
            'position': 'short',
            'trigger_price': current_price + 30,
            'action': 'close_short',
            'reason': '空仓止损-30元/吨'
        })
        
        levels.append({
            'type': 'take_profit',
            'position': 'short',
            'trigger_price': current_price - 50,
            'action': 'close_short',
            'reason': '空仓止盈+50元/吨'
        })
        
        return levels
    
    def print_next_day_signals(self):
        """打印次日交易信号"""
        print("\n📋 次日交易计划:")
        print("=" * 50)
        
        for i, signal in enumerate(self.next_day_signals, 1):
            print(f"{i}. {signal['type']}")
            print(f"   触发价格: {signal.get('trigger_price', 'N/A'):.0f}")
            print(f"   执行动作: {signal['action']}")
            print(f"   原因: {signal['reason']}")
            if 'probability' in signal:
                print(f"   概率: {signal['probability']:.1%}")
            print()
    
    def check_signal_triggers(self, current_price, current_positions):
        """
        检查信号触发条件
        在盘中实时调用
        """
        triggered_signals = []
        
        for signal in self.next_day_signals:
            if self.is_signal_triggered(signal, current_price, current_positions):
                triggered_signals.append(signal)
        
        return triggered_signals
    
    def is_signal_triggered(self, signal, current_price, positions):
        """检查单个信号是否触发"""
        signal_type = signal['type']
        trigger_price = signal.get('trigger_price')
        
        if trigger_price is None:
            return False
        
        # 条件开仓信号
        if signal_type == 'conditional_buy':
            return current_price >= trigger_price and positions['long'] == 0
        
        elif signal_type == 'conditional_sell':
            return current_price <= trigger_price and positions['short'] == 0
        
        # 止损止盈信号
        elif signal_type == 'stop_loss':
            if signal['position'] == 'long' and positions['long'] > 0:
                return current_price <= trigger_price
            elif signal['position'] == 'short' and positions['short'] > 0:
                return current_price >= trigger_price
        
        elif signal_type == 'take_profit':
            if signal['position'] == 'long' and positions['long'] > 0:
                return current_price >= trigger_price
            elif signal['position'] == 'short' and positions['short'] > 0:
                return current_price <= trigger_price
        
        return False
    
    def generate_trading_plan_report(self, historical_data):
        """生成次日交易计划报告"""
        signals = self.generate_next_day_signals(historical_data)
        
        report = []
        report.append("# 次日交易计划报告")
        report.append(f"## 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")
        
        if not signals:
            report.append("## ⚠️ 无交易信号")
            report.append("明日建议观望，等待更好的交易机会。")
        else:
            report.append("## 📋 交易信号列表")
            
            for i, signal in enumerate(signals, 1):
                report.append(f"### {i}. {signal['type']}")
                report.append(f"- **触发价格**: {signal.get('trigger_price', 'N/A'):.0f}")
                report.append(f"- **执行动作**: {signal['action']}")
                report.append(f"- **原因**: {signal['reason']}")
                if 'probability' in signal:
                    report.append(f"- **概率**: {signal['probability']:.1%}")
                report.append("")
        
        report.append("## ⚠️ 风险提示")
        report.append("1. 以上信号基于历史数据预测，实际市场可能有变化")
        report.append("2. 请结合实时市场情况调整执行策略")
        report.append("3. 严格执行止损纪律，控制风险")
        
        # 保存报告
        with open('results/next_day_trading_plan.md', 'w', encoding='utf-8') as f:
            f.write('\n'.join(report))
        
        print("📄 次日交易计划已保存: results/next_day_trading_plan.md")
        
        return report

# 使用示例
if __name__ == "__main__":
    # 初始化策略
    strategy = PredictiveAIStrategy()
    
    # 加载历史数据
    historical_data = pd.read_csv('../data/real_rebar_20250729.csv')
    
    # 生成次日交易计划
    strategy.generate_trading_plan_report(historical_data)
    
    # 模拟盘中检查信号触发
    current_price = 3500
    current_positions = {'long': 0, 'short': 0}
    
    triggered = strategy.check_signal_triggers(current_price, current_positions)
    if triggered:
        print(f"🚨 触发信号: {len(triggered)} 个")
        for signal in triggered:
            print(f"   - {signal['action']}: {signal['reason']}")
    else:
        print("✅ 暂无信号触发")
