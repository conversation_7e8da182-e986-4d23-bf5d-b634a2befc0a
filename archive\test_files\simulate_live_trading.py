#!/usr/bin/env python3
"""
模拟实盘交易测试
基于1分钟K线数据模拟真实交易环境
"""

import sys
sys.path.append('src')

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import time
import random
from ai_realtime_strategy import AIRealtimeStrategy

class LiveTradingSimulator:
    """实盘交易模拟器"""
    
    def __init__(self):
        self.strategy = AIRealtimeStrategy()
        self.initial_balance = 100000  # 初始资金10万
        self.balance = self.initial_balance
        self.positions = {'long': 0, 'short': 0}
        self.entry_prices = {'long': 0, 'short': 0}
        self.entry_times = {'long': None, 'short': None}
        self.trades = []
        self.price_history = []
        
        print("🎮 实盘交易模拟器初始化")
        print(f"💰 初始资金: {self.initial_balance:,} 元")
    
    def generate_minute_data(self, base_price=3200, minutes=120):
        """
        生成模拟的1分钟K线数据
        模拟真实的价格波动，包含MA30穿越场景
        """
        print(f"📊 生成{minutes}分钟的模拟1分钟K线数据...")
        print(f"   🎯 设计场景: 包含MA30穿越和止损止盈触发")

        data = []
        current_price = base_price
        current_time = datetime.now().replace(hour=9, minute=0, second=0, microsecond=0)
        ma30_level = self.strategy.previous_ma30  # 使用真实的MA30水平

        print(f"   📈 MA30水平: {ma30_level:.0f}")
        print(f"   💰 起始价格: {current_price:.0f}")

        for i in range(minutes):
            # 设计特定的价格走势来触发交易信号
            if i < 20:
                # 前20分钟：从高位下跌接近MA30
                target_price = ma30_level + 20 - i  # 逐步下跌
                price_change = (target_price - current_price) * 0.3 + random.uniform(-3, 3)
            elif i < 40:
                # 20-40分钟：在MA30附近震荡，触发下穿信号
                if i == 25:
                    price_change = -15  # 明确下穿MA30
                else:
                    price_change = random.uniform(-5, 5)
            elif i < 80:
                # 40-80分钟：下跌趋势，测试止损
                price_change = -0.5 + random.uniform(-3, 2)
            else:
                # 80分钟后：反弹上涨，触发上穿和止盈
                if i == 85:
                    price_change = 20  # 明确上穿MA30
                else:
                    price_change = 0.8 + random.uniform(-2, 4)

            # 计算OHLC
            open_price = current_price
            close_price = open_price + price_change

            # 确保价格变化合理
            high_price = max(open_price, close_price) + random.uniform(0, 5)
            low_price = min(open_price, close_price) - random.uniform(0, 5)
            volume = random.randint(1000, 5000)

            data.append({
                'Date': current_time,
                'Open': round(open_price, 0),
                'High': round(high_price, 0),
                'Low': round(low_price, 0),
                'Close': round(close_price, 0),
                'Volume': volume
            })

            current_price = close_price
            current_time += timedelta(minutes=1)

        df = pd.DataFrame(data)
        print(f"   ✅ 生成完成: {len(df)} 条1分钟K线")
        print(f"   📈 价格范围: {df['Low'].min():.0f} - {df['High'].max():.0f}")
        print(f"   🎯 预期触发: MA30穿越、止损、止盈信号")

        return df
    
    def initialize_trading_session(self):
        """初始化交易会话"""
        print("\n📊 初始化交易会话...")
        
        # 生成历史数据用于计算MA30
        historical_data = pd.read_csv('data/real_rebar_20250729.csv')
        historical_data['Date'] = pd.to_datetime(historical_data['Date'])
        
        # 初始化AI策略
        if not self.strategy.initialize_trading_day(historical_data):
            print("❌ 交易会话初始化失败")
            return False
        
        print("✅ 交易会话初始化完成")
        print(f"   📈 昨日MA30: {self.strategy.previous_ma30:.2f}")
        print(f"   💰 昨日收盘: {self.strategy.last_price:.2f}")
        
        return True
    
    def execute_trade(self, signal, current_price, current_time):
        """执行交易"""
        action = signal['action']
        
        if action == 'buy' and self.positions['long'] == 0 and self.positions['short'] == 0:
            # 双开：同时开多仓和空仓
            self.positions['long'] = 1
            self.positions['short'] = 1
            self.entry_prices['long'] = current_price
            self.entry_prices['short'] = current_price
            self.entry_times['long'] = current_time
            self.entry_times['short'] = current_time
            
            print(f"   🔄 双开执行: 多仓+空仓 @ {current_price:.0f}")
            
        elif action == 'sell' and self.positions['long'] == 0 and self.positions['short'] == 0:
            # 双开：同时开多仓和空仓
            self.positions['long'] = 1
            self.positions['short'] = 1
            self.entry_prices['long'] = current_price
            self.entry_prices['short'] = current_price
            self.entry_times['long'] = current_time
            self.entry_times['short'] = current_time
            
            print(f"   🔄 双开执行: 多仓+空仓 @ {current_price:.0f}")
    
    def check_stop_loss_profit(self, current_price, current_time):
        """检查止损止盈"""
        trades_executed = []
        
        # 检查多仓
        if self.positions['long'] > 0:
            profit = current_price - self.entry_prices['long']
            
            if profit <= -30:  # 止损
                self.close_position('long', current_price, current_time, '止损')
                trades_executed.append(f"多仓止损: {profit:.0f}元/吨")
            elif profit >= 50:  # 止盈
                self.close_position('long', current_price, current_time, '止盈')
                trades_executed.append(f"多仓止盈: {profit:.0f}元/吨")
        
        # 检查空仓
        if self.positions['short'] > 0:
            profit = self.entry_prices['short'] - current_price
            
            if profit <= -30:  # 止损
                self.close_position('short', current_price, current_time, '止损')
                trades_executed.append(f"空仓止损: {profit:.0f}元/吨")
            elif profit >= 50:  # 止盈
                self.close_position('short', current_price, current_time, '止盈')
                trades_executed.append(f"空仓止盈: {profit:.0f}元/吨")
        
        return trades_executed
    
    def close_position(self, position_type, exit_price, exit_time, reason):
        """平仓"""
        if self.positions[position_type] > 0:
            entry_price = self.entry_prices[position_type]
            entry_time = self.entry_times[position_type]
            
            if position_type == 'long':
                profit = exit_price - entry_price
            else:
                profit = entry_price - exit_price
            
            # 计算盈亏（10吨/手）
            profit_amount = profit * 10
            self.balance += profit_amount
            
            # 记录交易
            trade = {
                'type': position_type,
                'entry_price': entry_price,
                'exit_price': exit_price,
                'entry_time': entry_time,
                'exit_time': exit_time,
                'profit_per_ton': profit,
                'profit_amount': profit_amount,
                'reason': reason,
                'hold_minutes': (exit_time - entry_time).total_seconds() / 60
            }
            
            self.trades.append(trade)
            self.positions[position_type] = 0
            
            print(f"   📈 平{position_type}仓: {entry_price:.0f}→{exit_price:.0f}, {reason}, 盈亏{profit:+.0f}元/吨")
    
    def run_simulation(self, minutes=120):
        """运行模拟交易"""
        print(f"\n🚀 开始{minutes}分钟模拟实盘交易")
        print("=" * 60)
        
        # 初始化交易会话
        if not self.initialize_trading_session():
            return
        
        # 生成模拟数据
        minute_data = self.generate_minute_data(minutes=minutes)
        
        print(f"\n⏰ 模拟交易开始...")
        print(f"💰 初始资金: {self.balance:,} 元")
        print()
        
        # 逐分钟模拟交易
        for i, row in minute_data.iterrows():
            current_price = row['Close']
            current_time = row['Date']
            
            # 记录价格历史
            self.price_history.append({
                'time': current_time,
                'price': current_price,
                'ma30': self.strategy.calculate_intraday_ma30(current_price)
            })
            
            # 获取AI交易信号
            signal = self.strategy.get_intraday_trading_signal(current_price, current_time)
            
            # 检查止损止盈
            stop_trades = self.check_stop_loss_profit(current_price, current_time)
            
            # 执行新的交易信号
            if signal['action'] != 'hold':
                print(f"🚨 {current_time.strftime('%H:%M')} 交易信号: {signal['action']} @ {current_price:.0f}")
                print(f"   📝 原因: {signal['reason']}")
                self.execute_trade(signal, current_price, current_time)
            
            # 显示止损止盈执行
            for trade_info in stop_trades:
                print(f"   💰 {trade_info}")
            
            # 每10分钟显示一次状态
            if i % 10 == 0:
                self.print_status(current_time, current_price)
        
        # 最终平仓
        self.final_close_all(minute_data.iloc[-1]['Close'], minute_data.iloc[-1]['Date'])
        
        # 生成交易报告
        self.generate_trading_report()
    
    def print_status(self, current_time, current_price):
        """打印当前状态"""
        ma30 = self.strategy.calculate_intraday_ma30(current_price)
        long_profit = (current_price - self.entry_prices['long']) if self.positions['long'] > 0 else 0
        short_profit = (self.entry_prices['short'] - current_price) if self.positions['short'] > 0 else 0
        
        print(f"📊 {current_time.strftime('%H:%M')} 价格:{current_price:.0f} MA30:{ma30:.0f} "
              f"多仓:{long_profit:+.0f} 空仓:{short_profit:+.0f} 资金:{self.balance:,.0f}")
    
    def final_close_all(self, final_price, final_time):
        """最终平仓所有持仓"""
        print(f"\n🔚 最终平仓 @ {final_price:.0f}")
        
        if self.positions['long'] > 0:
            self.close_position('long', final_price, final_time, '最终平仓')
        
        if self.positions['short'] > 0:
            self.close_position('short', final_price, final_time, '最终平仓')
    
    def generate_trading_report(self):
        """生成交易报告"""
        print("\n" + "=" * 60)
        print("📋 模拟实盘交易报告")
        print("=" * 60)
        
        # 基本统计
        total_return = (self.balance - self.initial_balance) / self.initial_balance
        total_trades = len(self.trades)
        
        print(f"💰 初始资金: {self.initial_balance:,} 元")
        print(f"💰 最终资金: {self.balance:,} 元")
        print(f"📈 总收益: {self.balance - self.initial_balance:+,.0f} 元")
        print(f"📊 总收益率: {total_return:+.2%}")
        print(f"📊 交易次数: {total_trades} 次")
        
        if total_trades > 0:
            # 盈亏分析
            profits = [trade['profit_amount'] for trade in self.trades]
            win_trades = [p for p in profits if p > 0]
            lose_trades = [p for p in profits if p <= 0]
            
            win_rate = len(win_trades) / total_trades
            avg_profit = np.mean(profits)
            max_profit = max(profits)
            max_loss = min(profits)
            
            print(f"🎯 胜率: {win_rate:.2%}")
            print(f"📊 平均盈亏: {avg_profit:+.0f} 元/次")
            print(f"📈 最大盈利: {max_profit:+.0f} 元")
            print(f"📉 最大亏损: {max_loss:+.0f} 元")
            
            # 交易明细
            print(f"\n📝 交易明细:")
            for i, trade in enumerate(self.trades, 1):
                print(f"   {i}. {trade['type']}仓: "
                      f"{trade['entry_price']:.0f}→{trade['exit_price']:.0f} "
                      f"({trade['reason']}) "
                      f"{trade['profit_amount']:+.0f}元 "
                      f"持仓{trade['hold_minutes']:.0f}分钟")
        
        # 价格分析
        if self.price_history:
            prices = [p['price'] for p in self.price_history]
            price_range = max(prices) - min(prices)
            print(f"\n📊 价格分析:")
            print(f"   价格范围: {min(prices):.0f} - {max(prices):.0f} 元/吨")
            print(f"   价格波幅: {price_range:.0f} 元/吨")
        
        # 策略评估
        print(f"\n🎯 策略评估:")
        if total_return > 0.05:
            print("   ✅ 策略表现: 优秀")
        elif total_return > 0.02:
            print("   ✅ 策略表现: 良好")
        elif total_return > 0:
            print("   ⚠️ 策略表现: 一般")
        else:
            print("   ❌ 策略表现: 需要改进")

def main():
    """主函数"""
    print("🎮 模拟实盘交易测试")
    print("🎯 目标: 验证基于1分钟K线的实盘交易效果")
    print()
    
    # 创建模拟器
    simulator = LiveTradingSimulator()
    
    # 运行不同时长的模拟
    test_scenarios = [
        (60, "1小时快速测试"),
        (120, "2小时标准测试"),
        (240, "4小时压力测试")
    ]
    
    print("请选择测试场景:")
    for i, (minutes, description) in enumerate(test_scenarios, 1):
        print(f"{i}. {description} ({minutes}分钟)")
    
    try:
        choice = int(input("\n请选择 (1-3): ").strip())
        if 1 <= choice <= len(test_scenarios):
            minutes, description = test_scenarios[choice - 1]
            print(f"\n🚀 开始 {description}")
            simulator.run_simulation(minutes=minutes)
        else:
            print("❌ 无效选择")
    except ValueError:
        print("❌ 请输入有效数字")
    except KeyboardInterrupt:
        print("\n👋 用户中断测试")

if __name__ == "__main__":
    main()
