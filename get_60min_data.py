#!/usr/bin/env python3
"""
获取螺纹钢60分钟K线数据
用于60分钟级别的回测
"""

import sys
sys.path.append('src')

import akshare as ak
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

def get_rebar_60min_data():
    """获取螺纹钢60分钟历史数据"""
    print("📊 开始获取螺纹钢60分钟K线数据...")
    
    try:
        # 获取主力合约60分钟数据
        print("🔍 获取主力合约信息...")
        
        # 方法1: 尝试获取连续合约数据
        try:
            # 获取螺纹钢主力连续合约的60分钟数据
            data_60min = ak.futures_zh_minute_sina(symbol="RB0", period="60")
            
            if data_60min is not None and len(data_60min) > 0:
                print(f"✅ 成功获取60分钟数据: {len(data_60min)} 条记录")
                
                # 数据预处理
                data_60min = data_60min.reset_index()
                
                # 重命名列
                if 'datetime' in data_60min.columns:
                    data_60min = data_60min.rename(columns={'datetime': 'Date'})
                elif 'time' in data_60min.columns:
                    data_60min = data_60min.rename(columns={'time': 'Date'})
                
                # 确保列名正确
                column_mapping = {
                    'open': 'Open',
                    'high': 'High', 
                    'low': 'Low',
                    'close': 'Close',
                    'volume': 'Volume'
                }
                
                for old_col, new_col in column_mapping.items():
                    if old_col in data_60min.columns:
                        data_60min[new_col] = data_60min[old_col]
                
                # 确保有必要的列
                required_columns = ['Date', 'Open', 'High', 'Low', 'Close', 'Volume']
                missing_columns = [col for col in required_columns if col not in data_60min.columns]
                
                if missing_columns:
                    print(f"⚠️ 缺少列: {missing_columns}")
                    print(f"📋 当前列: {list(data_60min.columns)}")
                    
                    # 尝试从现有列推断
                    if 'Volume' not in data_60min.columns and 'vol' in data_60min.columns:
                        data_60min['Volume'] = data_60min['vol']
                
                # 只保留需要的列
                available_columns = [col for col in required_columns if col in data_60min.columns]
                data_60min = data_60min[available_columns]
                
                # 数据清理
                data_60min = data_60min.dropna()
                
                # 确保Date列是datetime类型
                if 'Date' in data_60min.columns:
                    data_60min['Date'] = pd.to_datetime(data_60min['Date'])
                
                # 按时间排序
                data_60min = data_60min.sort_values('Date').reset_index(drop=True)
                
                # 过滤最近2年的数据（足够用于回测）
                end_date = data_60min['Date'].max()
                start_date = end_date - timedelta(days=730)  # 2年
                data_60min = data_60min[data_60min['Date'] >= start_date].reset_index(drop=True)
                
                print(f"📈 数据时间范围: {data_60min['Date'].min()} 至 {data_60min['Date'].max()}")
                print(f"📊 最终数据条数: {len(data_60min)}")
                print(f"📋 数据列: {list(data_60min.columns)}")
                
                # 显示数据样本
                print("\n📋 数据样本:")
                print(data_60min.head())
                print("\n📋 数据统计:")
                print(data_60min.describe())
                
                # 保存数据
                output_file = 'data/rebar_60min_data.csv'
                data_60min.to_csv(output_file, index=False)
                print(f"\n💾 60分钟数据已保存: {output_file}")
                
                return data_60min
                
            else:
                print("❌ 未获取到60分钟数据")
                return None
                
        except Exception as e:
            print(f"❌ 获取60分钟数据失败: {str(e)}")
            return None
            
    except Exception as e:
        print(f"❌ 数据获取过程失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def convert_daily_to_60min():
    """将日K数据转换为模拟60分钟数据（备用方案）"""
    print("\n🔄 备用方案: 将日K数据转换为模拟60分钟数据...")
    
    try:
        # 读取现有日K数据
        daily_data = pd.read_csv('data/real_rebar_20250729.csv')
        daily_data['Date'] = pd.to_datetime(daily_data['Date'])
        
        print(f"📊 日K数据: {len(daily_data)} 条记录")
        
        # 为每个交易日生成4根60分钟K线（模拟9:00, 10:30, 13:30, 15:00）
        minute_data_list = []
        
        for _, row in daily_data.iterrows():
            date = row['Date']
            open_price = row['Open']
            high_price = row['High']
            low_price = row['Low']
            close_price = row['Close']
            volume = row['Volume']
            
            # 生成4根60分钟K线
            times = ['09:00', '10:30', '13:30', '15:00']
            
            # 简单的价格分布算法
            price_range = high_price - low_price
            
            for i, time_str in enumerate(times):
                timestamp = pd.to_datetime(f"{date.strftime('%Y-%m-%d')} {time_str}")
                
                if i == 0:  # 第一根K线
                    k_open = open_price
                    k_close = open_price + (close_price - open_price) * 0.25
                elif i == 1:  # 第二根K线
                    k_open = minute_data_list[-1]['Close']
                    k_close = open_price + (close_price - open_price) * 0.5
                elif i == 2:  # 第三根K线
                    k_open = minute_data_list[-1]['Close']
                    k_close = open_price + (close_price - open_price) * 0.75
                else:  # 第四根K线
                    k_open = minute_data_list[-1]['Close']
                    k_close = close_price
                
                # 计算高低价
                k_high = max(k_open, k_close) + price_range * 0.1 * np.random.random()
                k_low = min(k_open, k_close) - price_range * 0.1 * np.random.random()
                
                # 确保高低价在合理范围内
                k_high = min(k_high, high_price)
                k_low = max(k_low, low_price)
                
                minute_data_list.append({
                    'Date': timestamp,
                    'Open': round(k_open, 1),
                    'High': round(k_high, 1),
                    'Low': round(k_low, 1),
                    'Close': round(k_close, 1),
                    'Volume': volume // 4  # 平均分配成交量
                })
        
        # 转换为DataFrame
        minute_data = pd.DataFrame(minute_data_list)
        
        print(f"✅ 生成模拟60分钟数据: {len(minute_data)} 条记录")
        print(f"📈 时间范围: {minute_data['Date'].min()} 至 {minute_data['Date'].max()}")
        
        # 保存数据
        output_file = 'data/rebar_60min_simulated.csv'
        minute_data.to_csv(output_file, index=False)
        print(f"💾 模拟60分钟数据已保存: {output_file}")
        
        return minute_data
        
    except Exception as e:
        print(f"❌ 模拟数据生成失败: {str(e)}")
        return None

if __name__ == "__main__":
    print("🚀 螺纹钢60分钟数据获取工具")
    print("=" * 50)
    
    # 尝试获取真实60分钟数据
    data = get_rebar_60min_data()
    
    if data is None or len(data) < 100:
        print("\n⚠️ 真实60分钟数据获取失败或数据不足，使用备用方案...")
        data = convert_daily_to_60min()
    
    if data is not None:
        print("\n✅ 60分钟数据准备完成！")
        print("📋 可以开始60分钟级别的回测了")
    else:
        print("\n❌ 60分钟数据获取失败")
