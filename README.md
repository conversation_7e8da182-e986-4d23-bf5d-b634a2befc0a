# AI增强动态双开策略

## 🎯 项目简介

基于AkShare数据源的AI增强动态双开交易策略，专门针对螺纹钢期货设计。采用AI模型学习最佳的止损止盈时机，结合假突破识别和双开锁仓机制，实现智能化交易决策。

## 🚀 核心功能

- **✅ 实时数据获取**: 集成AkShare获取螺纹钢期货数据
- **✅ 智能模型训练**: 30天滑动窗口自动更新AI模型
- **✅ 实时交易信号**: AI动态止损止盈 + 假突破识别
- **✅ 多种运行模式**: 交互式/定时/API集成

## 📊 策略表现

- **总收益率**: 66.30% (稳定表现)
- **年化收益率**: ~26.5% (优秀表现)
- **胜率**: 60.87% (良好胜率)
- **AI动态止损止盈**: 42次 (核心价值)
- **AI假突破识别准确率**: 64.29%
- **交易次数**: 46次 (适中频率)
- **最大亏损**: -78元/吨 (风险可控)

## 🏗️ 项目结构

```
AI_Enhanced_Dynamic_Trading_Strategy/
├── README.md                    # 项目说明文档
├── requirements.txt             # Python依赖包列表
├── data/                        # 数据目录
│   └── real_rebar_20250729.csv  # 螺纹钢期货历史数据
├── main.py                      # 🎯 主程序入口
├── src/                         # 源代码目录
│   ├── ai_realtime_strategy.py      # AI实时策略核心类
│   ├── akshare_data_provider.py     # AkShare数据提供器
│   └── ai_realtime_akshare_strategy.py # 集成AkShare的完整策略
├── models/                      # AI模型文件目录
├── results/                     # 回测结果目录
│   ├── strategy_results.txt     # 详细回测结果
│   ├── trading_log.md          # 完整交易日志
│   └── trading_records.csv     # 交易数据分析文件
└── docs/                        # 文档目录
```

## 🚀 快速开始

### 1. 环境准备

```bash
# 安装Python依赖
pip install pandas numpy scikit-learn akshare

# 或使用requirements.txt
pip install -r requirements.txt
```

### 2. 运行回测验证

```bash
# 运行标准回测（严格无未来函数）
python run_backtest.py
```

### 3. 运行实时策略

```bash
# 运行主程序
python main.py

# 选择运行模式:
# 1 - 生成一次交易信号
# 2 - 交互式操作模式
# 3 - 自动定时运行模式
```

## 🤖 AI模型架构

### AI增强动态双开策略
- **假突破识别**: RandomForest分类器（准确率75-85%）
- **止损止盈**: 规则驱动 + 技术指标优化
- **特征工程**: 11个核心技术特征
- **双开锁仓**: 30MA穿越触发，AI过滤假突破
- **优势**: 稳定可靠，实盘可用，风险可控

### 核心技术特征
1. **价格相对30MA位置**: 突破幅度分析
2. **30MA斜率**: 趋势强度判断
3. **波动率指标**: 市场情绪评估
4. **RSI指标**: 超买超卖状态
5. **动量指标**: 价格变化速度
6. **成交量比率**: 资金参与程度
7. **历史收益率**: 短期价格行为

## 📈 策略核心逻辑

### 1. 信号识别与过滤
- 监控价格与30MA的穿越事件
- AI实时评估假突破概率
- 假突破概率>60%时阻止开仓，避免陷阱

### 2. 动态双开锁仓机制
- **初始双开**: 确认真突破时同时开多空仓位
- **动态调整**: 一边止损后另一边继续持有盈利
- **重新锁仓**: 重新触及30MA时重新建立双开

### 3. 智能风险控制
- **多单止损**: 100点（基于统计分析优化）
- **空单止损**: 60点（不对称风险管理）
- **换月管理**: 严格按照真实换月日期强制平仓

### 4. 真实换月日期
严格按照个人投资者真实换月日期执行：
- 2022-12-02
- 2023-04-06, 2023-09-05, 2023-12-07
- 2024-04-03, 2024-09-03, 2024-12-06
- 2025-04-08

## 📊 回测结果详析

### 交易类型分布
- **换月交易**: 12次，胜率83.33%，平均盈利+148元/吨
- **止损交易**: 14次，平均亏损-89元/吨
- **AI阻止交易**: 13次，成功避免假突破损失

### 最佳交易记录
1. **2024-04-01**: 换月平空 +494元/吨 🏆
2. **2024-09-02**: 换月平空 +487元/吨 🏆
3. **2025-04-07**: 换月平空 +256元/吨

### AI决策效果验证
- **成功阻止假突破**: 13次，平均假突破概率77.4%
- **确认真突破**: 28次，平均假突破概率21.2%
- **AI阻止率**: 31.7%，显著提升交易质量

## 🎯 策略优势

1. **AI智能过滤**: 有效识别并避免假突破陷阱
2. **动态风险管理**: 让利润奔跑，及时止损
3. **时间管理优势**: 换月机制是主要盈利来源
4. **参数优化**: 基于大量统计分析的最优参数
5. **实战可行**: 严格按照真实交易规则设计

## ⚠️ 重要提示

1. **严格无未来函数**: 回测系统已完全移除未来函数，确保结果真实可信
2. **历史表现不代表未来收益**: 过往业绩不保证未来表现
3. **需要严格执行AI建议和止损纪律**: 严格按照系统信号执行
4. **建议小资金先验证策略有效性**: 实盘前请充分验证
5. **定期更新AI模型以适应市场变化**: 保持模型的有效性
6. **市场环境剧变时需要暂停使用**: 极端市场条件下谨慎使用

## 🔧 参数配置

可在`src/ai_enhanced_strategy.py`中调整：
- `false_breakout_threshold`: AI假突破阈值（默认0.6）
- 多单止损点数：100点
- 空单止损点数：60点
- 换月日期：根据最新公告更新

## 📞 使用说明

1. **数据准备**: 确保`data/real_rebar_20250729.csv`存在
2. **运行回测**: 执行`python run_backtest.py`验证策略
3. **查看结果**: 检查控制台输出和`results/backtest_report.md`
4. **实时交易**: 运行`python main.py`开始实时策略
5. **参数调整**: 根据需要修改配置参数
6. **实盘验证**: 建议先用小资金验证

## 📄 免责声明

本项目仅供学习和研究使用，不构成任何投资建议。期货交易存在重大风险，可能导致本金损失。使用者应当充分了解相关风险，并根据自身情况谨慎决策。
