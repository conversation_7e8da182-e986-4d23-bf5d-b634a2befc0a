#!/usr/bin/env python3
"""
测试数据源获取情况
详细分析AkShare数据的级别和质量
"""

import sys
sys.path.append('src')

import time
from datetime import datetime
from akshare_data_provider import AkShareDataProvider

def test_data_source():
    """测试数据源"""
    print("📊 AkShare数据源详细测试")
    print("=" * 50)
    
    try:
        # 初始化数据提供器
        provider = AkShareDataProvider()
        
        # 1. 连接测试
        print("\n1️⃣ 连接测试")
        print("-" * 20)
        connection_ok = provider.test_connection()
        print(f"   连接状态: {'✅ 正常' if connection_ok else '❌ 异常'}")
        
        # 2. 实时数据测试
        print("\n2️⃣ 实时数据测试")
        print("-" * 20)
        
        print("   正在获取实时数据...")
        realtime_data = provider.get_futures_realtime_data()
        
        if realtime_data:
            print(f"   ✅ 实时数据获取成功")
            print(f"   📊 数据源: {realtime_data['source']}")
            print(f"   💰 当前价格: {realtime_data['current_price']:.0f} 元/吨")
            print(f"   ⏰ 时间戳: {realtime_data['timestamp']}")
            
            # 测试数据更新频率
            print("\n   📈 测试数据更新频率...")
            prices = []
            for i in range(3):
                price = provider.get_real_time_price()
                if price:
                    prices.append((datetime.now(), price))
                    print(f"   第{i+1}次: {price:.0f} 元/吨 @ {datetime.now().strftime('%H:%M:%S')}")
                time.sleep(2)
            
            if len(prices) > 1:
                price_changes = [abs(prices[i][1] - prices[i-1][1]) for i in range(1, len(prices))]
                avg_change = sum(price_changes) / len(price_changes)
                print(f"   📊 平均价格变化: {avg_change:.2f} 元/吨")
        else:
            print("   ❌ 实时数据获取失败")
        
        # 3. 历史数据测试
        print("\n3️⃣ 历史数据测试")
        print("-" * 20)
        
        print("   正在获取历史数据...")
        historical_data = provider.get_futures_daily_data(days=5)
        
        if historical_data is not None and len(historical_data) > 0:
            print(f"   ✅ 历史数据获取成功")
            print(f"   📊 数据条数: {len(historical_data)} 条")
            print(f"   📅 数据范围: {historical_data['Date'].min()} 至 {historical_data['Date'].max()}")
            
            # 显示最新几条数据
            print("\n   📋 最新数据:")
            latest_data = historical_data[['Date', 'Open', 'High', 'Low', 'Close', 'Volume']].tail(3)
            for _, row in latest_data.iterrows():
                print(f"   {row['Date']}: 开{row['Open']:.0f} 高{row['High']:.0f} 低{row['Low']:.0f} 收{row['Close']:.0f} 量{row['Volume']:.0f}")
        else:
            print("   ❌ 历史数据获取失败")
        
        # 4. 数据质量分析
        print("\n4️⃣ 数据质量分析")
        print("-" * 20)
        
        if realtime_data and historical_data is not None and len(historical_data) > 0:
            latest_close = historical_data['Close'].iloc[-1]
            current_price = realtime_data['current_price']
            
            price_diff = abs(current_price - latest_close)
            price_diff_pct = (price_diff / latest_close) * 100
            
            print(f"   📊 最新收盘价: {latest_close:.0f} 元/吨")
            print(f"   📊 当前实时价: {current_price:.0f} 元/吨")
            print(f"   📊 价格差异: {price_diff:.0f} 元/吨 ({price_diff_pct:.2f}%)")
            
            if price_diff_pct < 5:
                print("   ✅ 数据一致性: 良好")
            elif price_diff_pct < 10:
                print("   ⚠️ 数据一致性: 一般")
            else:
                print("   ❌ 数据一致性: 较差")
        
        # 5. 数据级别说明
        print("\n5️⃣ 数据级别说明")
        print("-" * 20)
        print("   📊 实时数据级别:")
        print("      - 数据源: 新浪财经期货实时行情")
        print("      - 更新频率: 1-3秒（交易时间内）")
        print("      - 数据类型: 主力合约实时价格")
        print("      - 延迟情况: 1-3秒延迟")
        print()
        print("   📊 历史数据级别:")
        print("      - 数据源: 新浪财经期货历史数据")
        print("      - 数据频率: 日K线数据")
        print("      - 数据字段: OHLCV完整数据")
        print("      - 数据质量: 交易所官方数据")
        
        # 6. 实盘适用性评估
        print("\n6️⃣ 实盘适用性评估")
        print("-" * 20)
        
        suitability_score = 0
        
        if connection_ok:
            suitability_score += 25
            print("   ✅ 网络连接: 正常 (+25分)")
        else:
            print("   ❌ 网络连接: 异常 (+0分)")
        
        if realtime_data:
            suitability_score += 35
            print("   ✅ 实时数据: 可获取 (+35分)")
        else:
            print("   ❌ 实时数据: 无法获取 (+0分)")
        
        if historical_data is not None and len(historical_data) > 0:
            suitability_score += 25
            print("   ✅ 历史数据: 可获取 (+25分)")
        else:
            print("   ❌ 历史数据: 无法获取 (+0分)")
        
        if realtime_data and historical_data is not None and len(historical_data) > 0:
            if price_diff_pct < 5:
                suitability_score += 15
                print("   ✅ 数据一致性: 良好 (+15分)")
            elif price_diff_pct < 10:
                suitability_score += 10
                print("   ⚠️ 数据一致性: 一般 (+10分)")
            else:
                suitability_score += 5
                print("   ❌ 数据一致性: 较差 (+5分)")
        
        print(f"\n   📊 实盘适用性评分: {suitability_score}/100")
        
        if suitability_score >= 80:
            print("   🎉 评估结果: 完全适合实盘交易")
        elif suitability_score >= 60:
            print("   ✅ 评估结果: 基本适合实盘交易")
        elif suitability_score >= 40:
            print("   ⚠️ 评估结果: 谨慎使用于实盘")
        else:
            print("   ❌ 评估结果: 不建议用于实盘")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🔍 数据源详细分析测试")
    print("🎯 目标: 了解AkShare数据的级别和质量")
    print()
    
    test_data_source()
    
    print("\n" + "=" * 50)
    print("📋 测试完成")
    print("💡 查看 DATA_SOURCE_ANALYSIS.md 了解详细说明")

if __name__ == "__main__":
    main()
