#!/usr/bin/env python3
"""
测试突破逻辑
验证MA30穿越检测是否正常工作
"""

import sys
sys.path.append('src')

import pandas as pd
from datetime import datetime
from ai_realtime_strategy import AIRealtimeStrategy

def test_breakout_logic():
    """测试突破逻辑"""
    print("🧪 测试MA30突破检测逻辑")
    print("=" * 50)
    
    # 初始化策略
    strategy = AIRealtimeStrategy()
    
    # 加载历史数据并初始化
    historical_data = pd.read_csv('data/real_rebar_20250729.csv')
    historical_data['Date'] = pd.to_datetime(historical_data['Date'])
    
    if not strategy.initialize_trading_day(historical_data):
        print("❌ 初始化失败")
        return
    
    ma30_level = strategy.previous_ma30
    print(f"📈 MA30水平: {ma30_level:.2f}")
    print(f"💰 昨日收盘: {strategy.last_price:.2f}")
    
    # 测试场景
    test_scenarios = [
        # (价格, 描述, 预期结果)
        (3200, "起始价格（MA30上方）", "无信号"),
        (3150, "仍在MA30上方", "无信号"),
        (3100, "下穿MA30", "应该有卖出信号"),
        (3080, "继续下跌", "无信号"),
        (3120, "反弹上穿MA30", "应该有买入信号"),
        (3150, "继续上涨", "无信号"),
    ]
    
    print(f"\n🎯 测试场景:")
    print("-" * 50)
    
    for i, (price, description, expected) in enumerate(test_scenarios):
        print(f"\n{i+1}. {description}")
        print(f"   价格: {price:.0f}")
        
        # 获取交易信号
        signal = strategy.get_intraday_trading_signal(price)
        
        print(f"   MA30: {signal['ma30']:.0f}")
        print(f"   动作: {signal['action']}")
        print(f"   原因: {signal['reason']}")
        print(f"   预期: {expected}")
        
        # 检查是否符合预期
        if "买入信号" in expected and signal['action'] == 'buy':
            print("   ✅ 符合预期")
        elif "卖出信号" in expected and signal['action'] == 'sell':
            print("   ✅ 符合预期")
        elif "无信号" in expected and signal['action'] == 'hold':
            print("   ✅ 符合预期")
        else:
            print("   ⚠️ 与预期不符")
    
    # 详细测试穿越检测
    print(f"\n🔍 详细测试穿越检测逻辑")
    print("-" * 50)
    
    # 重新初始化以测试穿越
    strategy = AIRealtimeStrategy()
    strategy.initialize_trading_day(historical_data)
    
    # 模拟价格序列，确保触发穿越
    price_sequence = [
        (3200, "起始：MA30上方"),
        (3150, "下降：仍在MA30上方"),
        (3120, "接近：接近MA30"),
        (3100, "穿越：下穿MA30"),  # 这里应该触发
        (3080, "确认：继续下跌"),
        (3090, "反弹：开始反弹"),
        (3110, "上升：接近MA30"),
        (3120, "穿越：上穿MA30"),  # 这里应该触发
        (3140, "确认：继续上涨"),
    ]
    
    for i, (price, description) in enumerate(price_sequence):
        print(f"\n步骤 {i+1}: {description}")
        
        # 检查突破
        breakout_detected, breakout_type, current_ma30 = strategy.check_intraday_breakout(price)
        
        print(f"   价格: {price:.0f}")
        print(f"   MA30: {current_ma30:.0f}")
        print(f"   上次价格: {strategy.last_price:.0f}")
        print(f"   突破检测: {breakout_detected}")
        if breakout_detected:
            print(f"   突破类型: {breakout_type}")
        
        # 获取完整信号
        signal = strategy.get_intraday_trading_signal(price)
        print(f"   最终信号: {signal['action']}")
        
        if signal['action'] != 'hold':
            print(f"   🚨 交易信号触发！")
    
    print(f"\n📊 测试总结:")
    print("   ✅ 系统组件测试完成")
    print("   ✅ 突破检测逻辑验证完成")
    print("   💡 如果没有触发信号，可能是AI判断为假突破")

def test_ai_false_breakout_detection():
    """测试AI假突破检测"""
    print(f"\n🤖 测试AI假突破检测")
    print("-" * 30)
    
    strategy = AIRealtimeStrategy()
    
    # 加载历史数据并初始化
    historical_data = pd.read_csv('data/real_rebar_20250729.csv')
    historical_data['Date'] = pd.to_datetime(historical_data['Date'])
    strategy.initialize_trading_day(historical_data)
    
    ma30_level = strategy.previous_ma30
    
    # 测试不同程度的突破
    test_cases = [
        (ma30_level - 5, "小幅下穿"),
        (ma30_level - 15, "中等下穿"),
        (ma30_level - 30, "大幅下穿"),
        (ma30_level + 5, "小幅上穿"),
        (ma30_level + 15, "中等上穿"),
        (ma30_level + 30, "大幅上穿"),
    ]
    
    for price, description in test_cases:
        # 设置前一个价格在MA30另一侧
        if price < ma30_level:
            strategy.last_price = ma30_level + 10  # 从上方穿越
            breakout_type = 'down'
        else:
            strategy.last_price = ma30_level - 10  # 从下方穿越
            breakout_type = 'up'
        
        # 检测假突破
        is_false = strategy.quick_false_breakout_check(price, breakout_type)
        
        deviation = abs(price - ma30_level) / ma30_level * 100
        
        print(f"   {description}: 价格{price:.0f}, 偏离{deviation:.1f}%, 假突破:{is_false}")

if __name__ == "__main__":
    test_breakout_logic()
    test_ai_false_breakout_detection()
