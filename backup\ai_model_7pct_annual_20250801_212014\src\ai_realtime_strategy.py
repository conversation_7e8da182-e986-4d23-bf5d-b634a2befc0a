#!/usr/bin/env python3
"""
AI增强动态双开策略 - 实时交易信号生成系统
支持增量更新训练和实时信号生成
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler
import pickle
import os
import warnings
warnings.filterwarnings('ignore')

class AIRealtimeStrategy:
    """AI实时交易策略类"""
    
    def __init__(self, training_window_years=2.5, update_frequency_days=30):
        """
        初始化AI实时策略
        
        Args:
            training_window_years: 训练窗口年数 (推荐2-3年)
            update_frequency_days: 模型更新频率天数 (推荐30天)
        """
        self.training_window_years = training_window_years
        self.update_frequency_days = update_frequency_days
        self.training_window_days = int(training_window_years * 252)  # 252个交易日/年
        
        # 模型存储
        self.breakout_models = {'up': None, 'down': None, 'up_scaler': None, 'down_scaler': None}
        self.stop_models = {'long': None, 'short': None, 'long_scaler': None, 'short_scaler': None}
        
        # 模型文件路径
        self.model_dir = 'models'
        self.ensure_model_dir()
        
        # 最后更新时间
        self.last_update_date = None
        
        print(f"🤖 AI实时策略初始化:")
        print(f"   训练窗口: {training_window_years}年 ({self.training_window_days}个交易日)")
        print(f"   更新频率: {update_frequency_days}天")
    
    def ensure_model_dir(self):
        """确保模型目录存在"""
        if not os.path.exists(self.model_dir):
            os.makedirs(self.model_dir)
    
    def should_update_model(self, current_date):
        """判断是否需要更新模型"""
        if self.last_update_date is None:
            return True
        
        days_since_update = (current_date - self.last_update_date).days
        return days_since_update >= self.update_frequency_days
    
    def get_training_data_window(self, data, current_date):
        """获取训练数据窗口"""
        # 计算训练窗口的开始日期
        start_date = current_date - timedelta(days=self.training_window_days)
        
        # 筛选训练数据
        training_data = data[
            (data['Date'] >= start_date) & 
            (data['Date'] <= current_date)
        ].copy()
        
        print(f"📊 训练数据窗口: {start_date.strftime('%Y-%m-%d')} 至 {current_date.strftime('%Y-%m-%d')}")
        print(f"   数据量: {len(training_data)} 条记录")
        
        return training_data
    
    def calculate_technical_indicators(self, data):
        """计算技术指标"""
        # 基础指标
        data['MA30'] = data['Close'].rolling(window=30).mean()
        data['Returns'] = data['Close'].pct_change()
        data['Volatility_5'] = data['Returns'].rolling(window=5).std()
        data['Volatility_20'] = data['Returns'].rolling(window=20).std()
        data['Price_vs_MA30'] = (data['Close'] - data['MA30']) / data['MA30']
        data['MA30_Slope'] = data['MA30'].diff(5) / data['MA30'].shift(5)
        data['RSI'] = self.calculate_rsi(data['Close'], 14)
        
        # 成交量代理
        data['Volume_Proxy'] = abs(data['Returns']) * 1000000
        data['Volume_MA5'] = data['Volume_Proxy'].rolling(window=5).mean()
        data['Volume_Ratio'] = data['Volume_Proxy'] / data['Volume_MA5']
        
        # 动量指标
        data['Momentum_5'] = data['Close'] / data['Close'].shift(5) - 1
        data['Momentum_10'] = data['Close'] / data['Close'].shift(10) - 1
        
        # 趋势强度
        data['Trend_Strength'] = abs(data['MA30_Slope'])
        
        return data
    
    def calculate_rsi(self, prices, window=14):
        """计算RSI"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def update_models(self, data, current_date):
        """增量更新AI模型"""
        print(f"\n🔄 开始更新AI模型 ({current_date.strftime('%Y-%m-%d')})")
        
        # 获取训练数据窗口
        training_data = self.get_training_data_window(data, current_date)
        
        if len(training_data) < 100:
            print("❌ 训练数据不足，跳过模型更新")
            return False
        
        # 计算技术指标
        training_data = self.calculate_technical_indicators(training_data)
        
        # 训练假突破识别模型
        print("🤖 训练假突破识别模型...")
        self.breakout_models = self.train_breakout_models(training_data)
        
        # 训练动态止损止盈模型
        print("🧠 训练动态止损止盈模型...")
        self.stop_models = self.train_dynamic_stop_models(training_data)
        
        # 保存模型
        self.save_models(current_date)
        
        # 更新最后更新时间
        self.last_update_date = current_date
        
        print(f"✅ 模型更新完成 ({current_date.strftime('%Y-%m-%d')})")
        return True
    
    def train_breakout_models(self, data):
        """训练假突破识别模型"""
        up_breakouts = []
        down_breakouts = []
        
        # 收集训练数据
        for i in range(50, len(data) - 10):
            current_price = data.iloc[i]['Close']
            current_ma30 = data.iloc[i]['MA30']
            prev_price = data.iloc[i-1]['Close']
            prev_ma30 = data.iloc[i-1]['MA30']
            
            if pd.isna(current_ma30) or pd.isna(prev_ma30):
                continue
            
            # 提取特征
            features = self.extract_breakout_features(data, i)
            if features is None:
                continue
            
            # 上穿
            if prev_price <= prev_ma30 and current_price > current_ma30:
                is_false = self.is_false_up_breakout(data, i)
                up_breakouts.append({'features': features, 'is_false': is_false})
            
            # 下穿
            elif prev_price >= prev_ma30 and current_price < current_ma30:
                is_false = self.is_false_down_breakout(data, i)
                down_breakouts.append({'features': features, 'is_false': is_false})
        
        # 训练模型
        up_model, up_scaler = None, StandardScaler()
        down_model, down_scaler = None, StandardScaler()
        
        if len(up_breakouts) > 10:
            up_model, up_scaler = self.train_single_model(up_breakouts, "上穿假突破")
            print(f"   ✅ 上穿模型: {len(up_breakouts)} 个样本")
        
        if len(down_breakouts) > 10:
            down_model, down_scaler = self.train_single_model(down_breakouts, "下穿假突破")
            print(f"   ✅ 下穿模型: {len(down_breakouts)} 个样本")
        
        return {
            'up': up_model, 'down': down_model,
            'up_scaler': up_scaler, 'down_scaler': down_scaler
        }
    
    def train_dynamic_stop_models(self, data):
        """训练动态止损止盈模型"""
        long_positions = []
        short_positions = []
        
        # 收集持仓训练数据
        for i in range(100, len(data) - 30):
            # 模拟多单持仓
            long_data = self.simulate_position(data, i, 'long')
            if long_data:
                long_positions.extend(long_data)
            
            # 模拟空单持仓
            short_data = self.simulate_position(data, i, 'short')
            if short_data:
                short_positions.extend(short_data)
        
        # 训练模型
        long_model, long_scaler = None, StandardScaler()
        short_model, short_scaler = None, StandardScaler()
        
        if len(long_positions) > 50:
            long_model, long_scaler = self.train_single_model(long_positions, "多单动态止损止盈")
            print(f"   ✅ 多单模型: {len(long_positions)} 个样本")
        
        if len(short_positions) > 50:
            short_model, short_scaler = self.train_single_model(short_positions, "空单动态止损止盈")
            print(f"   ✅ 空单模型: {len(short_positions)} 个样本")
        
        return {
            'long': long_model, 'short': short_model,
            'long_scaler': long_scaler, 'short_scaler': short_scaler
        }
    
    def extract_breakout_features(self, data, index):
        """提取假突破识别特征"""
        try:
            row = data.iloc[index]
            features = [
                row['Price_vs_MA30'],
                row['MA30_Slope'],
                row['Volatility_5'],
                row['RSI'],
                row['Volume_Ratio'],
                row['Momentum_5'],
                row['Returns']
            ]
            
            if any(pd.isna(f) for f in features):
                return None
            
            return features
        except:
            return None
    
    def simulate_position(self, data, entry_index, position_type):
        """模拟持仓，生成训练数据"""
        entry_price = data.iloc[entry_index]['Close']
        position_data = []
        
        max_profit = 0
        max_loss = 0
        
        # 模拟持仓20天
        for j in range(1, 21):
            if entry_index + j >= len(data):
                break
            
            current_index = entry_index + j
            current_price = data.iloc[current_index]['Close']
            
            if position_type == 'long':
                profit = current_price - entry_price
            else:
                profit = entry_price - current_price
            
            max_profit = max(max_profit, profit)
            max_loss = min(max_loss, profit)
            
            # 提取特征
            features = self.extract_position_features(data, current_index, entry_price, j, max_profit, max_loss)
            if features is None:
                continue
            
            # 确定最佳决策
            decision = self.determine_best_decision(data, current_index, entry_price, position_type)
            
            position_data.append({
                'features': features,
                'decision': decision
            })
        
        return position_data

    def extract_position_features(self, data, index, entry_price, holding_days, max_profit, max_loss):
        """提取持仓管理特征"""
        try:
            row = data.iloc[index]
            current_price = row['Close']
            current_profit = current_price - entry_price  # 多单盈亏

            features = [
                # 持仓信息 (4个)
                holding_days,
                current_profit,
                max_profit,
                max_loss,

                # 市场技术指标 (8个)
                row['Price_vs_MA30'],
                row['MA30_Slope'],
                row['Volatility_5'],
                row['RSI'],
                row['Volume_Ratio'],
                row['Momentum_5'],
                row['Trend_Strength'],
                row['Returns'],

                # 风险指标 (3个)
                holding_days / 20.0,  # 持仓时间比例
                current_profit / max(abs(max_profit), abs(max_loss), 1),  # 盈亏比例
                row['Volatility_5'] / max(row['Volatility_20'], 0.001)  # 波动率比
            ]

            if any(pd.isna(f) for f in features):
                return None

            return features
        except:
            return None

    def determine_best_decision(self, data, current_index, entry_price, position_type):
        """确定最佳决策"""
        current_price = data.iloc[current_index]['Close']

        if position_type == 'long':
            current_profit = current_price - entry_price
        else:
            current_profit = entry_price - current_price

        # 查看未来3天的表现
        future_profits = []
        for k in range(1, 4):
            if current_index + k >= len(data):
                break

            future_price = data.iloc[current_index + k]['Close']
            if position_type == 'long':
                future_profit = future_price - entry_price
            else:
                future_profit = entry_price - future_price

            future_profits.append(future_profit)

        if not future_profits:
            return 0  # 继续持有

        max_future_profit = max(future_profits)
        min_future_profit = min(future_profits)

        # 优化后的决策逻辑
        if current_profit < -30:  # 优化: 更严格止损 (原来-40)
            if max_future_profit < current_profit + 10:  # 未来难以回本
                return 1  # 止损
            else:
                return 0  # 继续持有

        elif current_profit > 20:  # 优化: 更早止盈 (原来25)
            if min_future_profit < current_profit - 15:  # 未来可能回撤
                return 2  # 止盈
            else:
                return 0  # 继续持有

        else:  # 小幅盈亏
            return 0  # 继续持有

    def is_false_up_breakout(self, data, index):
        """判断上穿是否为假突破"""
        for j in range(1, 4):
            if index + j >= len(data):
                break
            future_price = data.iloc[index + j]['Close']
            future_ma30 = data.iloc[index + j]['MA30']
            if pd.notna(future_ma30) and future_price < future_ma30:
                return True
        return False

    def is_false_down_breakout(self, data, index):
        """判断下穿是否为假突破"""
        for j in range(1, 4):
            if index + j >= len(data):
                break
            future_price = data.iloc[index + j]['Close']
            future_ma30 = data.iloc[index + j]['MA30']
            if pd.notna(future_ma30) and future_price > future_ma30:
                return True
        return False

    def train_single_model(self, training_data, model_type):
        """训练单个模型"""
        X = np.array([item['features'] for item in training_data])

        if 'is_false' in training_data[0]:
            y = np.array([item['is_false'] for item in training_data])
        else:
            y = np.array([item['decision'] for item in training_data])

        # 检查数据平衡性
        unique_labels = np.unique(y)
        if len(unique_labels) < 2:
            return None, StandardScaler()

        # 标准化特征
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)

        # 训练模型
        model = RandomForestClassifier(n_estimators=100, random_state=42, class_weight='balanced')
        model.fit(X_scaled, y)

        return model, scaler

    def save_models(self, current_date):
        """保存模型到文件"""
        date_str = current_date.strftime('%Y%m%d')

        # 保存假突破识别模型
        breakout_file = os.path.join(self.model_dir, f'breakout_models_{date_str}.pkl')
        with open(breakout_file, 'wb') as f:
            pickle.dump(self.breakout_models, f)

        # 保存动态止损止盈模型
        stop_file = os.path.join(self.model_dir, f'stop_models_{date_str}.pkl')
        with open(stop_file, 'wb') as f:
            pickle.dump(self.stop_models, f)

        print(f"💾 模型已保存: {breakout_file}, {stop_file}")

    def load_latest_models(self):
        """加载最新的模型"""
        try:
            # 查找最新的模型文件
            breakout_files = [f for f in os.listdir(self.model_dir) if f.startswith('breakout_models_')]
            stop_files = [f for f in os.listdir(self.model_dir) if f.startswith('stop_models_')]

            if not breakout_files or not stop_files:
                print("⚠️ 未找到已保存的模型文件")
                return False

            # 获取最新文件
            latest_breakout = max(breakout_files)
            latest_stop = max(stop_files)

            # 加载模型
            breakout_file = os.path.join(self.model_dir, latest_breakout)
            with open(breakout_file, 'rb') as f:
                self.breakout_models = pickle.load(f)

            stop_file = os.path.join(self.model_dir, latest_stop)
            with open(stop_file, 'rb') as f:
                self.stop_models = pickle.load(f)

            # 提取日期
            date_str = latest_breakout.split('_')[-1].replace('.pkl', '')
            self.last_update_date = datetime.strptime(date_str, '%Y%m%d')

            print(f"📂 已加载模型: {latest_breakout}, {latest_stop}")
            print(f"   最后更新: {self.last_update_date.strftime('%Y-%m-%d')}")
            return True

        except Exception as e:
            print(f"❌ 加载模型失败: {str(e)}")
            return False

    def generate_realtime_signal(self, data, current_date, current_positions=None):
        """生成实时交易信号"""
        print(f"\n📡 生成实时交易信号 ({current_date.strftime('%Y-%m-%d')})")

        # 检查是否需要更新模型
        if self.should_update_model(current_date):
            print("🔄 检测到需要更新模型...")
            if not self.update_models(data, current_date):
                print("❌ 模型更新失败，使用现有模型")

        # 确保模型已加载
        if self.breakout_models['up'] is None and self.breakout_models['down'] is None:
            if not self.load_latest_models():
                print("❌ 无可用模型，无法生成信号")
                return None

        # 获取当前数据
        current_data = data[data['Date'] <= current_date].copy()
        if len(current_data) < 100:
            print("❌ 历史数据不足，无法生成信号")
            return None

        # 计算技术指标
        current_data = self.calculate_technical_indicators(current_data)

        # 获取最新数据点
        latest_index = len(current_data) - 1
        current_price = current_data.iloc[latest_index]['Close']
        current_ma30 = current_data.iloc[latest_index]['MA30']
        prev_price = current_data.iloc[latest_index-1]['Close']
        prev_ma30 = current_data.iloc[latest_index-1]['MA30']

        if pd.isna(current_ma30) or pd.isna(prev_ma30):
            print("❌ 技术指标计算失败")
            return None

        signals = {
            'date': current_date,
            'price': current_price,
            'ma30': current_ma30,
            'breakout_signal': None,
            'position_signals': {},
            'ai_decisions': {}
        }

        # 1. 检查MA30穿越信号
        if prev_price <= prev_ma30 and current_price > current_ma30:
            # 上穿信号
            ai_prob = self.predict_false_breakout(current_data, latest_index, 'up')
            if ai_prob > 0.5:  # 优化: 更容易阻止交易 (原来0.6)
                signals['breakout_signal'] = {
                    'type': 'up_cross_blocked',
                    'action': 'no_action',
                    'ai_probability': ai_prob,
                    'reason': 'AI识别假突破'
                }
            else:
                signals['breakout_signal'] = {
                    'type': 'up_cross_confirmed',
                    'action': 'double_open',
                    'ai_probability': ai_prob,
                    'reason': 'AI确认真突破'
                }

        elif prev_price >= prev_ma30 and current_price < current_ma30:
            # 下穿信号
            ai_prob = self.predict_false_breakout(current_data, latest_index, 'down')
            if ai_prob > 0.5:  # 优化: 更容易阻止交易 (原来0.6)
                signals['breakout_signal'] = {
                    'type': 'down_cross_blocked',
                    'action': 'no_action',
                    'ai_probability': ai_prob,
                    'reason': 'AI识别假突破'
                }
            else:
                signals['breakout_signal'] = {
                    'type': 'down_cross_confirmed',
                    'action': 'double_open',
                    'ai_probability': ai_prob,
                    'reason': 'AI确认真突破'
                }

        # 2. 检查持仓管理信号
        if current_positions:
            for position_type, position_info in current_positions.items():
                if position_info['quantity'] > 0:
                    # 计算持仓信息
                    entry_price = position_info['entry_price']
                    entry_date = position_info['entry_date']
                    holding_days = (current_date - entry_date).days

                    # 计算盈亏
                    if position_type == 'long':
                        current_profit = current_price - entry_price
                    else:
                        current_profit = entry_price - current_price

                    # AI预测持仓动作
                    ai_action = self.predict_position_action(
                        current_data, latest_index, entry_price, holding_days,
                        position_info.get('max_profit', current_profit),
                        position_info.get('max_loss', current_profit),
                        position_type
                    )

                    action_map = {0: 'hold', 1: 'stop_loss', 2: 'take_profit'}
                    signals['position_signals'][position_type] = {
                        'action': action_map.get(ai_action, 'hold'),
                        'current_profit': current_profit,
                        'holding_days': holding_days,
                        'ai_decision': ai_action,
                        'reason': f'AI建议{action_map.get(ai_action, "持有")}'
                    }

        return signals

    def predict_false_breakout(self, data, index, breakout_type):
        """预测假突破概率"""
        features = self.extract_breakout_features(data, index)
        if features is None:
            return 0.5

        try:
            if breakout_type == 'up' and self.breakout_models['up'] is not None:
                features_array = np.array(features).reshape(1, -1)
                features_scaled = self.breakout_models['up_scaler'].transform(features_array)
                prob = self.breakout_models['up'].predict_proba(features_scaled)[0][1]
                return prob
            elif breakout_type == 'down' and self.breakout_models['down'] is not None:
                features_array = np.array(features).reshape(1, -1)
                features_scaled = self.breakout_models['down_scaler'].transform(features_array)
                prob = self.breakout_models['down'].predict_proba(features_scaled)[0][1]
                return prob
            else:
                return 0.5
        except Exception as e:
            print(f"⚠️ 假突破预测失败: {str(e)}")
            return 0.5

    def predict_position_action(self, data, index, entry_price, holding_days, max_profit, max_loss, position_type):
        """预测持仓动作"""
        features = self.extract_position_features(data, index, entry_price, holding_days, max_profit, max_loss)
        if features is None:
            return 0  # 继续持有

        try:
            if position_type == 'long' and self.stop_models['long'] is not None:
                features_array = np.array(features).reshape(1, -1)
                features_scaled = self.stop_models['long_scaler'].transform(features_array)
                action = self.stop_models['long'].predict(features_scaled)[0]
                return action
            elif position_type == 'short' and self.stop_models['short'] is not None:
                features_array = np.array(features).reshape(1, -1)
                features_scaled = self.stop_models['short_scaler'].transform(features_array)
                action = self.stop_models['short'].predict(features_scaled)[0]
                return action
            else:
                return 0  # 继续持有
        except Exception as e:
            print(f"⚠️ 持仓动作预测失败: {str(e)}")
            return 0  # 出错时继续持有

    def format_signal_output(self, signals):
        """格式化信号输出"""
        if signals is None:
            return "❌ 无法生成交易信号"

        output = []
        output.append(f"📡 实时交易信号 ({signals['date'].strftime('%Y-%m-%d')})")
        output.append("=" * 60)
        output.append(f"当前价格: {signals['price']:.0f}")
        output.append(f"MA30: {signals['ma30']:.0f}")
        output.append("")

        # 突破信号
        if signals['breakout_signal']:
            bs = signals['breakout_signal']
            output.append("🎯 突破信号:")
            output.append(f"   类型: {bs['type']}")
            output.append(f"   动作: {bs['action']}")
            output.append(f"   AI概率: {bs['ai_probability']:.1%}")
            output.append(f"   原因: {bs['reason']}")
            output.append("")

        # 持仓信号
        if signals['position_signals']:
            output.append("📊 持仓管理信号:")
            for pos_type, ps in signals['position_signals'].items():
                output.append(f"   {pos_type.upper()}仓:")
                output.append(f"     动作: {ps['action']}")
                output.append(f"     当前盈亏: {ps['current_profit']:+.0f}元/吨")
                output.append(f"     持仓天数: {ps['holding_days']}天")
                output.append(f"     AI决策: {ps['ai_decision']}")
                output.append(f"     原因: {ps['reason']}")
                output.append("")

        if not signals['breakout_signal'] and not signals['position_signals']:
            output.append("💤 当前无交易信号")

        return "\n".join(output)


def demo_realtime_strategy():
    """演示实时策略功能"""
    print("🚀 AI增强动态双开策略 - 实时交易信号演示")
    print("=" * 60)

    # 初始化策略
    strategy = AIRealtimeStrategy(training_window_years=2.5, update_frequency_days=30)

    # 加载数据
    try:
        data = pd.read_csv('data/real_rebar_20250729.csv')
        data['Date'] = pd.to_datetime(data['Date'])
        data = data.sort_values('Date').reset_index(drop=True)
        print(f"✅ 数据加载成功: {len(data)} 条记录")
    except Exception as e:
        print(f"❌ 数据加载失败: {str(e)}")
        return

    # 尝试加载已有模型
    strategy.load_latest_models()

    # 模拟几个日期的实时信号生成
    test_dates = [
        datetime(2025, 7, 25),
        datetime(2025, 7, 26),
        datetime(2025, 7, 29)
    ]

    # 模拟持仓状态
    current_positions = {
        'long': {
            'quantity': 1,
            'entry_price': 3100,
            'entry_date': datetime(2025, 7, 20),
            'max_profit': 50,
            'max_loss': -20
        }
    }

    for test_date in test_dates:
        print(f"\n{'='*60}")

        # 生成实时信号
        signals = strategy.generate_realtime_signal(data, test_date, current_positions)

        # 输出信号
        signal_output = strategy.format_signal_output(signals)
        print(signal_output)

        # 模拟根据信号更新持仓
        if signals and signals.get('position_signals'):
            for pos_type, ps in signals['position_signals'].items():
                if ps['action'] in ['stop_loss', 'take_profit']:
                    print(f"🔄 模拟执行: {pos_type}仓{ps['action']}")
                    current_positions[pos_type]['quantity'] = 0

    print(f"\n{'='*60}")
    print("✅ 实时策略演示完成")


def main():
    """主函数"""
    demo_realtime_strategy()


if __name__ == "__main__":
    main()
