#!/usr/bin/env python3
"""
AI增强动态双开策略 - 集成AkShare实时数据
支持实时数据获取、增量训练和信号生成
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import time
import schedule
import sys
import os

# 导入自定义模块
from ai_realtime_strategy import AIRealtimeStrategy
from akshare_data_provider import AkShareDataProvider

class AIRealtimeAkShareStrategy:
    """集成AkShare的AI实时策略"""
    
    def __init__(self, training_window_years=2.5, update_frequency_days=30):
        """
        初始化集成AkShare的AI实时策略
        
        Args:
            training_window_years: 训练窗口年数
            update_frequency_days: 模型更新频率天数
        """
        self.data_provider = AkShareDataProvider()
        self.ai_strategy = AIRealtimeStrategy(training_window_years, update_frequency_days)
        self.current_positions = {}
        self.last_signal_time = None
        
        print("🚀 AI实时AkShare策略初始化完成")
        print(f"   数据源: AkShare")
        print(f"   训练窗口: {training_window_years}年")
        print(f"   更新频率: {update_frequency_days}天")
    
    def initialize_system(self):
        """初始化系统"""
        print("\n🔧 初始化AI策略系统...")
        
        # 1. 测试数据连接
        print("1. 测试AkShare数据连接...")
        if not self.data_provider.test_data_connection():
            print("❌ 数据连接测试失败")
            return False
        
        # 2. 获取初始数据
        print("2. 获取初始历史数据...")
        initial_data = self.data_provider.get_latest_data_for_strategy('rebar', days=1000)
        if initial_data is None or initial_data.empty:
            print("❌ 初始数据获取失败")
            return False
        
        # 3. 初始化AI模型
        print("3. 初始化AI模型...")
        if not self.ai_strategy.load_latest_models():
            print("   🔄 训练新模型...")
            latest_date = initial_data['Date'].max()
            if not self.ai_strategy.update_models(initial_data, latest_date):
                print("❌ 模型初始化失败")
                return False
        
        print("✅ 系统初始化完成")
        return True
    
    def get_realtime_data(self):
        """获取实时数据"""
        try:
            # 获取策略所需的最新数据
            data = self.data_provider.get_latest_data_for_strategy('rebar', days=1000)
            
            if data is None or data.empty:
                print("❌ 实时数据获取失败")
                return None
            
            return data
            
        except Exception as e:
            print(f"❌ 获取实时数据失败: {str(e)}")
            return None
    
    def generate_trading_signal(self, target_date=None):
        """生成交易信号"""
        try:
            if target_date is None:
                target_date = datetime.now()
            
            print(f"\n📡 生成交易信号 ({target_date.strftime('%Y-%m-%d %H:%M:%S')})")
            print("-" * 50)
            
            # 1. 获取最新数据
            data = self.get_realtime_data()
            if data is None:
                return None
            
            # 2. 生成AI信号
            signals = self.ai_strategy.generate_realtime_signal(
                data, target_date, self.current_positions
            )
            
            if signals is None:
                print("❌ 信号生成失败")
                return None
            
            # 3. 格式化输出
            signal_output = self.ai_strategy.format_signal_output(signals)
            print(signal_output)
            
            # 4. 记录信号时间
            self.last_signal_time = target_date
            
            return signals
            
        except Exception as e:
            print(f"❌ 交易信号生成失败: {str(e)}")
            return None
    
    def update_position(self, position_type, quantity, entry_price, entry_date=None):
        """更新持仓信息"""
        if entry_date is None:
            entry_date = datetime.now()
        
        if quantity > 0:
            self.current_positions[position_type] = {
                'quantity': quantity,
                'entry_price': entry_price,
                'entry_date': entry_date,
                'max_profit': 0,
                'max_loss': 0
            }
            print(f"📊 更新{position_type}仓: {quantity}手 @ {entry_price:.0f}")
        else:
            if position_type in self.current_positions:
                del self.current_positions[position_type]
                print(f"🔄 清空{position_type}仓")
    
    def execute_signal_action(self, signals):
        """执行信号动作 (模拟)"""
        if not signals:
            return
        
        print("\n🎯 信号执行建议:")
        print("-" * 30)
        
        # 处理突破信号
        if signals.get('breakout_signal'):
            bs = signals['breakout_signal']
            if bs['action'] == 'double_open':
                print(f"📈 建议双开: 多空各1手 @ {signals['price']:.0f}")
                print(f"   AI置信度: {bs['ai_probability']:.1%}")
                print(f"   原因: {bs['reason']}")
            elif bs['action'] == 'no_action':
                print(f"🚫 建议不开仓: {bs['reason']}")
                print(f"   AI假突破概率: {bs['ai_probability']:.1%}")
        
        # 处理持仓管理信号
        if signals.get('position_signals'):
            for pos_type, ps in signals['position_signals'].items():
                if ps['action'] == 'stop_loss':
                    print(f"🛑 建议{pos_type}仓止损: 亏损 {ps['current_profit']:.0f} 元/吨")
                elif ps['action'] == 'take_profit':
                    print(f"💰 建议{pos_type}仓止盈: 盈利 {ps['current_profit']:.0f} 元/吨")
                elif ps['action'] == 'hold':
                    print(f"📊 建议{pos_type}仓持有: 当前 {ps['current_profit']:+.0f} 元/吨")
    
    def run_daily_strategy(self):
        """运行日度策略"""
        print(f"\n🌅 执行日度策略 ({datetime.now().strftime('%Y-%m-%d %H:%M:%S')})")
        print("=" * 60)
        
        try:
            # 1. 生成交易信号
            signals = self.generate_trading_signal()
            
            if signals:
                # 2. 执行信号动作
                self.execute_signal_action(signals)
                
                # 3. 保存信号记录
                self.save_signal_record(signals)
                
                return signals
            else:
                print("❌ 日度策略执行失败")
                return None
                
        except Exception as e:
            print(f"❌ 日度策略执行错误: {str(e)}")
            return None
    
    def save_signal_record(self, signals):
        """保存信号记录"""
        try:
            # 创建信号记录目录
            record_dir = 'signals'
            if not os.path.exists(record_dir):
                os.makedirs(record_dir)
            
            # 保存信号到文件
            date_str = datetime.now().strftime('%Y%m%d_%H%M%S')
            record_file = os.path.join(record_dir, f'signal_{date_str}.txt')
            
            with open(record_file, 'w', encoding='utf-8') as f:
                f.write(self.ai_strategy.format_signal_output(signals))
            
            print(f"💾 信号记录已保存: {record_file}")
            
        except Exception as e:
            print(f"⚠️ 信号记录保存失败: {str(e)}")
    
    def start_scheduled_strategy(self):
        """启动定时策略"""
        print("\n⏰ 启动定时策略系统")
        print("=" * 40)
        
        # 设置定时任务
        # 每日收盘后15:30执行
        schedule.every().day.at("15:30").do(self.run_daily_strategy)
        
        # 每小时检查一次 (交易时间内)
        schedule.every().hour.do(self.check_positions)
        
        print("📅 定时任务设置:")
        print("   • 每日15:30: 生成交易信号")
        print("   • 每小时: 检查持仓状态")
        print("   • Ctrl+C: 停止运行")
        
        try:
            while True:
                schedule.run_pending()
                time.sleep(60)  # 每分钟检查一次
                
        except KeyboardInterrupt:
            print("\n👋 定时策略已停止")
    
    def check_positions(self):
        """检查持仓状态"""
        if not self.current_positions:
            return
        
        print(f"\n📊 持仓检查 ({datetime.now().strftime('%H:%M:%S')})")
        
        # 获取当前价格
        realtime_info = self.data_provider.get_futures_realtime_data('rebar')
        if not realtime_info:
            return
        
        current_price = realtime_info['current_price']
        
        for pos_type, pos_info in self.current_positions.items():
            entry_price = pos_info['entry_price']
            
            if pos_type == 'long':
                current_profit = current_price - entry_price
            else:
                current_profit = entry_price - current_price
            
            # 更新最大盈亏
            pos_info['max_profit'] = max(pos_info.get('max_profit', 0), current_profit)
            pos_info['max_loss'] = min(pos_info.get('max_loss', 0), current_profit)
            
            print(f"   {pos_type.upper()}仓: {current_profit:+.0f} 元/吨 (入场: {entry_price:.0f}, 当前: {current_price:.0f})")
    
    def run_interactive_mode(self):
        """运行交互模式"""
        print("\n🎮 交互模式")
        print("=" * 30)
        print("命令:")
        print("  signal - 生成交易信号")
        print("  position - 查看持仓")
        print("  long [数量] [价格] - 设置多仓")
        print("  short [数量] [价格] - 设置空仓")
        print("  clear [long/short] - 清空持仓")
        print("  auto - 启动自动模式")
        print("  quit - 退出")
        
        while True:
            try:
                command = input("\nAI策略> ").strip().split()
                
                if not command:
                    continue
                
                cmd = command[0].lower()
                
                if cmd in ['quit', 'exit']:
                    print("👋 再见!")
                    break
                
                elif cmd == 'signal':
                    self.generate_trading_signal()
                
                elif cmd == 'position':
                    if self.current_positions:
                        print("📊 当前持仓:")
                        for pos_type, pos_info in self.current_positions.items():
                            print(f"   {pos_type.upper()}仓: {pos_info['quantity']}手 @ {pos_info['entry_price']:.0f}")
                    else:
                        print("💤 当前无持仓")
                
                elif cmd in ['long', 'short']:
                    if len(command) >= 3:
                        quantity = int(command[1])
                        price = float(command[2])
                        self.update_position(cmd, quantity, price)
                    else:
                        print("❌ 格式: long/short [数量] [价格]")
                
                elif cmd == 'clear':
                    if len(command) > 1:
                        pos_type = command[1].lower()
                        if pos_type in ['long', 'short']:
                            self.update_position(pos_type, 0, 0)
                        else:
                            print("❌ 格式: clear [long/short]")
                    else:
                        print("❌ 格式: clear [long/short]")
                
                elif cmd == 'auto':
                    print("🚀 启动自动模式...")
                    self.start_scheduled_strategy()
                    break
                
                else:
                    print(f"❌ 未知命令: {cmd}")
            
            except KeyboardInterrupt:
                print("\n👋 再见!")
                break
            except Exception as e:
                print(f"❌ 错误: {str(e)}")

    def start_intraday_trading(self):
        """启动盘中实时交易模式"""
        print("\n⚡ 盘中实时交易模式")
        print("=" * 40)
        print("🎯 特点: 无需等待收盘，基于实时价格即时决策")
        print("🚫 严格避免未来函数，确保实盘可用")
        print()

        try:
            # 获取历史数据并初始化交易日
            print("📊 初始化交易日...")
            historical_data = self.data_provider.get_futures_daily_data(days=100)

            if historical_data is None or len(historical_data) < 30:
                print("❌ 历史数据不足，无法初始化")
                return

            # 初始化交易日（计算昨日MA30）
            if not self.strategy.initialize_trading_day(historical_data):
                print("❌ 交易日初始化失败")
                return

            print("✅ 交易日初始化完成")
            print("\n🚀 盘中实时交易系统已启动")
            print("💡 输入价格获取实时交易信号")
            print("💡 输入'auto'启动自动模式")
            print("💡 输入'q'退出")
            print("=" * 40)

            while True:
                try:
                    user_input = input("\n请输入当前价格 (或'auto'/'q'): ").strip()

                    if user_input.lower() == 'q':
                        print("👋 退出盘中交易模式")
                        break

                    elif user_input.lower() == 'auto':
                        print("🔄 启动自动模式...")
                        self.run_auto_intraday_trading()
                        break

                    # 解析价格
                    current_price = float(user_input)

                    # 获取实时交易信号
                    signal = self.strategy.get_intraday_trading_signal(current_price)

                    # 显示信号
                    self.display_intraday_signal(signal)

                except ValueError:
                    print("❌ 请输入有效的价格数字")
                except KeyboardInterrupt:
                    print("\n👋 用户中断，退出系统")
                    break
                except Exception as e:
                    print(f"❌ 处理失败: {str(e)}")

        except Exception as e:
            print(f"❌ 系统初始化失败: {str(e)}")

    def run_auto_intraday_trading(self):
        """运行自动盘中交易"""
        print("🔄 自动盘中交易模式")
        print("💡 系统将每30秒获取一次实时价格")
        print("🔄 按Ctrl+C停止")

        import time

        try:
            while True:
                # 获取实时价格
                current_price = self.data_provider.get_real_time_price()

                if current_price is not None:
                    # 获取交易信号
                    signal = self.strategy.get_intraday_trading_signal(current_price)

                    # 显示信号（只显示有动作的信号）
                    if signal['action'] != 'hold':
                        print(f"\n🚨 {datetime.now().strftime('%H:%M:%S')} 交易信号触发！")
                        self.display_intraday_signal(signal)
                    else:
                        print(f"📊 {datetime.now().strftime('%H:%M:%S')} 价格:{current_price:.0f} MA30:{signal['ma30']:.0f} 无信号")
                else:
                    print(f"⚠️ {datetime.now().strftime('%H:%M:%S')} 无法获取实时价格")

                # 等待30秒
                time.sleep(30)

        except KeyboardInterrupt:
            print("\n👋 自动交易已停止")
        except Exception as e:
            print(f"❌ 自动交易失败: {str(e)}")

    def display_intraday_signal(self, signal):
        """显示盘中交易信号"""
        print(f"\n📊 实时交易信号:")
        print(f"   ⏰ 时间: {signal['timestamp'].strftime('%H:%M:%S')}")
        print(f"   💰 价格: {signal['price']:.0f}")
        print(f"   📈 MA30: {signal['ma30']:.0f}")
        print(f"   🎯 动作: {signal['action']}")
        print(f"   📝 原因: {signal['reason']}")
        print(f"   🎲 置信度: {signal['confidence']:.1%}")

        if signal['action'] != 'hold':
            print(f"\n🚨 交易信号触发！")
            print(f"   💡 建议执行: {signal['action']} @ {signal['price']:.0f}")
            print(f"   ⚠️ 请根据实际情况决定是否执行")


def main():
    """主函数"""
    print("🚀 AI增强动态双开策略 - AkShare版本")
    print("=" * 60)
    
    # 初始化策略
    strategy = AIRealtimeAkShareStrategy(
        training_window_years=2.5,
        update_frequency_days=30
    )
    
    # 初始化系统
    if not strategy.initialize_system():
        print("❌ 系统初始化失败")
        return
    
    # 运行演示
    print("\n📊 运行演示...")
    signals = strategy.generate_trading_signal()
    
    if signals:
        strategy.execute_signal_action(signals)
    
    # 进入交互模式
    strategy.run_interactive_mode()


if __name__ == "__main__":
    main()
