#!/usr/bin/env python3
"""
将所有时间向后推移1分钟
9:00 -> 9:01, 9:01 -> 9:02, ..., 9:59 -> 10:00
"""

import pandas as pd
from datetime import timedelta

def adjust_time_plus_1min():
    """将所有时间向后推移1分钟"""
    print("🔧 调整时间：所有时间向后推移1分钟...")
    
    try:
        # 读取当前的数据文件
        data = pd.read_csv('data/rb_1min_akshare_format.csv')
        data['DateTime'] = pd.to_datetime(data['DateTime'])
        
        print(f"📊 原始数据: {len(data)} 条")
        print(f"📅 时间范围: {data['DateTime'].min()} 到 {data['DateTime'].max()}")
        
        # 将所有时间向后推移1分钟
        print("⏰ 执行时间调整...")
        data['DateTime'] = data['DateTime'] + timedelta(minutes=1)
        
        print(f"📅 调整后时间范围: {data['DateTime'].min()} 到 {data['DateTime'].max()}")
        
        # 验证调整结果
        print("\n🔍 验证调整结果...")
        verify_time_adjustment(data)
        
        # 保存调整后的数据
        output_file = 'data/rb_1min_akshare_format_adjusted.csv'
        data.to_csv(output_file, index=False, encoding='utf-8')
        print(f"\n💾 调整后数据已保存: {output_file}")
        
        return data
        
    except Exception as e:
        print(f"❌ 调整失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def verify_time_adjustment(data):
    """验证时间调整结果"""
    from datetime import time
    
    # 检查关键时间点
    data['Time'] = data['DateTime'].dt.time
    time_counts = data['Time'].value_counts().sort_index()
    
    # 调整后应该有的时间点
    expected_times = [
        ('09:01:00', '日盘开始'),
        ('10:00:00', '原9:59调整后'),
        ('15:00:00', '原14:59调整后'),
        ('21:01:00', '夜盘开始'),
        ('23:00:00', '原22:59调整后')
    ]
    
    # 调整后不应该有的时间点
    unexpected_times = [
        ('09:00:00', '原开盘时间'),
        ('14:59:00', '原日盘结束'),
        ('21:00:00', '原夜盘开始'),
        ('22:59:00', '原夜盘结束前')
    ]
    
    print("   应该有的时间点:")
    for time_str, desc in expected_times:
        t = time.fromisoformat(time_str)
        count = time_counts.get(t, 0)
        status = "✅" if count > 0 else "❌"
        print(f"   {status} {time_str} ({desc}): {count} 次")
    
    print("\n   不应该有的时间点:")
    for time_str, desc in unexpected_times:
        t = time.fromisoformat(time_str)
        count = time_counts.get(t, 0)
        status = "✅" if count == 0 else "⚠️"
        print(f"   {status} {time_str} ({desc}): {count} 次")
    
    # 显示时间分布统计
    print(f"\n   📊 时间分布:")
    print(f"   最早时间: {time_counts.index.min()}")
    print(f"   最晚时间: {time_counts.index.max()}")
    print(f"   唯一时间点数: {len(time_counts)}")

def compare_before_after():
    """对比调整前后的数据"""
    print("\n📊 对比调整前后的数据...")
    
    try:
        # 读取调整前的数据
        before_data = pd.read_csv('data/rb_1min_akshare_format.csv')
        before_data['DateTime'] = pd.to_datetime(before_data['DateTime'])
        
        # 读取调整后的数据
        after_data = pd.read_csv('data/rb_1min_akshare_format_adjusted.csv')
        after_data['DateTime'] = pd.to_datetime(after_data['DateTime'])
        
        print("🔍 对比前5条记录:")
        print("调整前:")
        for i in range(5):
            row = before_data.iloc[i]
            print(f"   {i+1}: {row['DateTime']} | 收盘: {row['Close']}")
        
        print("\n调整后:")
        for i in range(5):
            row = after_data.iloc[i]
            print(f"   {i+1}: {row['DateTime']} | 收盘: {row['Close']}")
        
        # 验证时间差
        time_diff = after_data['DateTime'].iloc[0] - before_data['DateTime'].iloc[0]
        print(f"\n⏰ 时间差验证: {time_diff}")
        
        if time_diff == timedelta(minutes=1):
            print("✅ 时间调整正确：向后推移1分钟")
        else:
            print("❌ 时间调整错误")
            
    except Exception as e:
        print(f"❌ 对比失败: {str(e)}")

def main():
    """主函数"""
    print("⏰ 时间调整工具")
    print("🎯 目标: 将所有时间向后推移1分钟")
    print("📝 规则: 9:00→9:01, 9:01→9:02, ..., 9:59→10:00")
    print("=" * 60)
    
    # 执行时间调整
    adjusted_data = adjust_time_plus_1min()
    
    if adjusted_data is not None:
        # 对比调整前后
        compare_before_after()
        
        print("\n" + "=" * 60)
        print("✅ 时间调整完成!")
        print("🎯 所有时间已向后推移1分钟")
        print("💡 现在时间格式应该与AkShare完全一致")
    else:
        print("\n❌ 时间调整失败")

if __name__ == "__main__":
    main()
