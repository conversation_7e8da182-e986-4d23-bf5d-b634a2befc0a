#!/usr/bin/env python3
"""
修正螺纹钢期货盈利计算
螺纹钢期货: 1个点 = 10元 (不是100元)
"""

import sys
sys.path.append('src')

from ai_realtime_strategy import AIRealtimeStrategy
import pandas as pd
import numpy as np
from datetime import datetime
import csv

def fix_profit_calculation():
    """修正盈利计算并重新生成交易日志"""
    print("🔧 修正螺纹钢期货盈利计算")
    print("=" * 60)
    print("📊 螺纹钢期货合约规格:")
    print("   交易单位: 10吨/手")
    print("   最小变动价位: 1元/吨")
    print("   1个点价值: 1元/吨 × 10吨 = 10元")
    print("   ❌ 之前错误地按100元计算了!")
    print()
    
    try:
        # 1. 重新运行回测并正确计算盈利
        corrected_result = run_corrected_backtest()
        
        # 2. 生成修正后的交易日志
        generate_corrected_trading_log(corrected_result)
        
        # 3. 对比修正前后的差异
        show_correction_comparison(corrected_result)
        
        print("✅ 盈利计算修正完成!")
        
    except Exception as e:
        print(f"❌ 修正失败: {str(e)}")
        import traceback
        traceback.print_exc()

def run_corrected_backtest():
    """运行修正后的回测"""
    print("🔄 运行修正后的回测...")
    
    # 加载数据
    data = pd.read_csv('data/real_rebar_20250729.csv')
    data['Date'] = pd.to_datetime(data['Date'])
    data = data.sort_values('Date').reset_index(drop=True)
    
    strategy = AIRealtimeStrategy()
    data = strategy.calculate_technical_indicators(data)
    
    # 初始化回测参数
    initial_balance = 100000
    balance = initial_balance
    long_position = 0
    short_position = 0
    long_entry_price = 0
    short_entry_price = 0
    long_entry_date = None
    short_entry_date = None
    
    trades = []
    trading_records = []
    trade_id = 1
    
    # 换月日期
    rollover_dates = [
        datetime(2022, 12, 2), datetime(2023, 4, 6), datetime(2023, 9, 5),
        datetime(2023, 12, 7), datetime(2024, 4, 3), datetime(2024, 9, 3),
        datetime(2024, 12, 6), datetime(2025, 4, 8)
    ]
    
    # 回测主循环
    for i in range(100, len(data)):
        current_price = data.loc[i, 'Close']
        current_ma30 = data.loc[i, 'MA30']
        prev_price = data.loc[i-1, 'Close']
        prev_ma30 = data.loc[i-1, 'MA30']
        current_date = data.loc[i, 'Date']
        
        if pd.isna(current_ma30) or pd.isna(prev_ma30):
            continue
        
        # 检查换月强制平仓
        is_rollover = any((rollover_date - current_date).days in range(1, 4) for rollover_date in rollover_dates)
        
        if is_rollover and (long_position > 0 or short_position > 0):
            if long_position > 0:
                profit_long = current_price - long_entry_price
                balance += profit_long * 10  # 修正: 1个点 = 10元
                trades.append(profit_long)
                
                trading_records.append({
                    'trade_id': trade_id,
                    'entry_date': long_entry_date.strftime('%Y-%m-%d'),
                    'exit_date': current_date.strftime('%Y-%m-%d'),
                    'position': 'LONG',
                    'entry_price': long_entry_price,
                    'exit_price': current_price,
                    'profit_loss_per_ton': profit_long,
                    'profit_loss_yuan': profit_long * 10,  # 修正计算
                    'holding_days': (current_date - long_entry_date).days,
                    'exit_reason': '换月强制平仓',
                    'ai_decision': '强制'
                })
                trade_id += 1
                long_position = 0
            
            if short_position > 0:
                profit_short = short_entry_price - current_price
                balance += profit_short * 10  # 修正: 1个点 = 10元
                trades.append(profit_short)
                
                trading_records.append({
                    'trade_id': trade_id,
                    'entry_date': short_entry_date.strftime('%Y-%m-%d'),
                    'exit_date': current_date.strftime('%Y-%m-%d'),
                    'position': 'SHORT',
                    'entry_price': short_entry_price,
                    'exit_price': current_price,
                    'profit_loss_per_ton': profit_short,
                    'profit_loss_yuan': profit_short * 10,  # 修正计算
                    'holding_days': (current_date - short_entry_date).days,
                    'exit_reason': '换月强制平仓',
                    'ai_decision': '强制'
                })
                trade_id += 1
                short_position = 0
            continue
        
        # AI止损止盈检查
        if long_position > 0:
            ai_action = strategy.determine_best_decision(data, i, long_entry_price, 'long')
            
            if ai_action in [1, 2]:  # 止损或止盈
                profit = current_price - long_entry_price
                balance += profit * 10  # 修正: 1个点 = 10元
                trades.append(profit)
                
                exit_reason = "AI止损" if ai_action == 1 else "AI止盈"
                current_rsi = data.loc[i, 'RSI']
                current_ma30_distance = (current_price - current_ma30) / current_ma30
                
                if ai_action == 2:  # 止盈的具体原因
                    if current_rsi > 70:
                        exit_reason = "AI止盈(RSI超买)"
                    elif abs(current_ma30_distance) > 0.05:
                        exit_reason = "AI止盈(偏离MA30)"
                    else:
                        exit_reason = "AI止盈(盈利保护)"
                
                trading_records.append({
                    'trade_id': trade_id,
                    'entry_date': long_entry_date.strftime('%Y-%m-%d'),
                    'exit_date': current_date.strftime('%Y-%m-%d'),
                    'position': 'LONG',
                    'entry_price': long_entry_price,
                    'exit_price': current_price,
                    'profit_loss_per_ton': profit,
                    'profit_loss_yuan': profit * 10,  # 修正计算
                    'holding_days': (current_date - long_entry_date).days,
                    'exit_reason': exit_reason,
                    'ai_decision': 'AI智能'
                })
                trade_id += 1
                long_position = 0
        
        if short_position > 0:
            ai_action = strategy.determine_best_decision(data, i, short_entry_price, 'short')
            
            if ai_action in [1, 2]:  # 止损或止盈
                profit = short_entry_price - current_price
                balance += profit * 10  # 修正: 1个点 = 10元
                trades.append(profit)
                
                exit_reason = "AI止损" if ai_action == 1 else "AI止盈"
                current_rsi = data.loc[i, 'RSI']
                current_ma30_distance = (current_price - current_ma30) / current_ma30
                
                if ai_action == 2:  # 止盈的具体原因
                    if current_rsi < 30:
                        exit_reason = "AI止盈(RSI超卖)"
                    elif abs(current_ma30_distance) > 0.05:
                        exit_reason = "AI止盈(偏离MA30)"
                    else:
                        exit_reason = "AI止盈(盈利保护)"
                
                trading_records.append({
                    'trade_id': trade_id,
                    'entry_date': short_entry_date.strftime('%Y-%m-%d'),
                    'exit_date': current_date.strftime('%Y-%m-%d'),
                    'position': 'SHORT',
                    'entry_price': short_entry_price,
                    'exit_price': current_price,
                    'profit_loss_per_ton': profit,
                    'profit_loss_yuan': profit * 10,  # 修正计算
                    'holding_days': (current_date - short_entry_date).days,
                    'exit_reason': exit_reason,
                    'ai_decision': 'AI智能'
                })
                trade_id += 1
                short_position = 0
        
        # 检查30MA穿越
        price_cross_ma30 = (
            (prev_price > prev_ma30 and current_price <= current_ma30) or
            (prev_price < prev_ma30 and current_price >= current_ma30)
        )
        
        if price_cross_ma30:
            # 确定穿越方向
            if prev_price <= prev_ma30 and current_price > current_ma30:
                breakout_type = 'up'
            else:
                breakout_type = 'down'
            
            # AI假突破识别
            ai_prob = strategy.predict_false_breakout(data, i, breakout_type)
            
            if ai_prob <= 0.5:  # AI认为是真突破
                if long_position == 0 and short_position == 0:
                    long_position = 1
                    short_position = 1
                    long_entry_price = current_price
                    short_entry_price = current_price
                    long_entry_date = current_date
                    short_entry_date = current_date
    
    # 计算最终统计
    if trades:
        total_profit_per_ton = sum(trades)
        total_profit_yuan = total_profit_per_ton * 10  # 修正计算
        total_return = (balance - initial_balance) / initial_balance
        win_trades = [p for p in trades if p > 0]
        win_rate = len(win_trades) / len(trades)
        max_profit = max(trades)
        max_loss = min(trades)
        avg_profit = np.mean(trades)
    else:
        total_profit_per_ton = 0
        total_profit_yuan = 0
        total_return = 0
        win_rate = 0
        max_profit = 0
        max_loss = 0
        avg_profit = 0
    
    result = {
        'initial_balance': initial_balance,
        'final_balance': balance,
        'total_return': total_return,
        'total_trades': len(trades),
        'win_rate': win_rate,
        'total_profit_per_ton': total_profit_per_ton,
        'total_profit_yuan': total_profit_yuan,
        'avg_profit': avg_profit,
        'max_profit': max_profit,
        'max_loss': max_loss,
        'trading_records': trading_records
    }
    
    print(f"   💰 最终资金: {balance:,.0f} 元")
    print(f"   📈 总收益率: {total_return:.2%}")
    print(f"   📊 总盈亏: {total_profit_per_ton:.0f} 元/吨 ({total_profit_yuan:+,.0f} 元)")
    print(f"   🎯 胜率: {win_rate:.2%}")
    print(f"   📈 最大盈利: {max_profit:.0f} 元/吨 ({max_profit * 10:+,.0f} 元)")
    print(f"   📉 最大亏损: {max_loss:.0f} 元/吨 ({max_loss * 10:+,.0f} 元)")
    
    return result

def generate_corrected_trading_log(result):
    """生成修正后的交易日志"""
    print("\n📝 生成修正后的交易日志...")
    
    # 生成CSV文件
    csv_columns = [
        'trade_id', 'entry_date', 'exit_date', 'position', 
        'entry_price', 'exit_price', 'profit_loss_per_ton', 'profit_loss_yuan',
        'holding_days', 'exit_reason', 'ai_decision'
    ]
    
    with open('results/corrected_trading_records.csv', 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=csv_columns)
        writer.writeheader()
        for record in result['trading_records']:
            writer.writerow(record)
    
    # 生成Markdown报告
    report_content = []
    report_content.append("# 修正后的AI交易系统回测报告")
    report_content.append(f"## 修正时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    report_content.append("")
    
    report_content.append("## 🔧 修正说明")
    report_content.append("**重要修正**: 螺纹钢期货盈利计算")
    report_content.append("- **合约规格**: 10吨/手，1元/吨 = 10元/手")
    report_content.append("- **之前错误**: 按100元/点计算")
    report_content.append("- **现在正确**: 按10元/点计算")
    report_content.append("- **影响**: 所有盈利数据需要除以10")
    report_content.append("")
    
    report_content.append("## 📊 修正后的回测结果")
    report_content.append(f"- **初始资金**: {result['initial_balance']:,.0f} 元")
    report_content.append(f"- **最终资金**: {result['final_balance']:,.0f} 元")
    report_content.append(f"- **总收益率**: {result['total_return']:.2%}")
    report_content.append(f"- **年化收益率**: {result['total_return'] / 2.5:.2%} (约)")
    report_content.append(f"- **总交易次数**: {result['total_trades']} 次")
    report_content.append(f"- **胜率**: {result['win_rate']:.2%}")
    report_content.append(f"- **总盈亏**: {result['total_profit_per_ton']:.0f} 元/吨 ({result['total_profit_yuan']:+,.0f} 元)")
    report_content.append(f"- **平均盈亏**: {result['avg_profit']:.0f} 元/吨 ({result['avg_profit'] * 10:+,.0f} 元)")
    report_content.append(f"- **最大盈利**: {result['max_profit']:.0f} 元/吨 ({result['max_profit'] * 10:+,.0f} 元)")
    report_content.append(f"- **最大亏损**: {result['max_loss']:.0f} 元/吨 ({result['max_loss'] * 10:+,.0f} 元)")
    report_content.append("")
    
    report_content.append("## 📋 交易记录")
    report_content.append("")
    report_content.append("| 序号 | 开仓日期 | 平仓日期 | 仓位 | 开仓价 | 平仓价 | 盈亏(元/吨) | 盈亏(元) | 持仓天数 | 平仓原因 |")
    report_content.append("|------|----------|----------|------|--------|--------|-------------|----------|----------|----------|")
    
    for record in result['trading_records']:
        report_content.append(
            f"| {record['trade_id']} | {record['entry_date']} | {record['exit_date']} | "
            f"{record['position']} | {record['entry_price']:.0f} | {record['exit_price']:.0f} | "
            f"{record['profit_loss_per_ton']:+.0f} | {record['profit_loss_yuan']:+,.0f} | "
            f"{record['holding_days']} | {record['exit_reason']} |"
        )
    
    # 保存报告
    with open('results/corrected_backtest_report.md', 'w', encoding='utf-8') as f:
        for line in report_content:
            f.write(line + '\n')
    
    print("   💾 修正后的交易日志已保存:")
    print("      - results/corrected_trading_records.csv")
    print("      - results/corrected_backtest_report.md")

def show_correction_comparison(corrected_result):
    """显示修正前后的对比"""
    print("\n📊 修正前后对比:")
    print("=" * 50)
    
    # 之前的错误数据 (按100元/点计算)
    old_total_profit_yuan = corrected_result['total_profit_per_ton'] * 100
    old_final_balance = 100000 + old_total_profit_yuan
    old_return = old_total_profit_yuan / 100000
    
    print(f"❌ 修正前 (错误的100元/点):")
    print(f"   总盈亏: {old_total_profit_yuan:+,.0f} 元")
    print(f"   最终资金: {old_final_balance:,.0f} 元")
    print(f"   收益率: {old_return:.2%}")
    print(f"   年化收益率: {old_return / 2.5:.2%}")
    print()
    
    print(f"✅ 修正后 (正确的10元/点):")
    print(f"   总盈亏: {corrected_result['total_profit_yuan']:+,.0f} 元")
    print(f"   最终资金: {corrected_result['final_balance']:,.0f} 元")
    print(f"   收益率: {corrected_result['total_return']:.2%}")
    print(f"   年化收益率: {corrected_result['total_return'] / 2.5:.2%}")
    print()
    
    print(f"📉 差异:")
    print(f"   盈利差异: {corrected_result['total_profit_yuan'] - old_total_profit_yuan:+,.0f} 元")
    print(f"   收益率差异: {corrected_result['total_return'] - old_return:+.2%}")
    
    # 评估修正后的表现
    annual_return = corrected_result['total_return'] / 2.5
    print(f"\n🎯 修正后的真实表现评估:")
    if annual_return > 0.15:
        print(f"   ✅ 年化{annual_return:.1%}仍然是优秀表现!")
    elif annual_return > 0.08:
        print(f"   📈 年化{annual_return:.1%}是良好表现")
    else:
        print(f"   📊 年化{annual_return:.1%}是一般表现")

if __name__ == "__main__":
    fix_profit_calculation()
