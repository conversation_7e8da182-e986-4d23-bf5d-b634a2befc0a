#!/usr/bin/env python3
"""
修复螺纹钢1分钟K线数据的时间节点问题
补充缺失的关键时间点
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta, time

def fix_rb_1min_data():
    """
    修复1分钟K线数据的时间节点问题
    """
    print("🔧 修复螺纹钢1分钟K线数据时间节点...")
    
    # 1. 读取处理后的数据
    data = pd.read_csv('data/rb_1min_2022_2025.csv')
    data['DateTime'] = pd.to_datetime(data['DateTime'])
    data = data.sort_values('DateTime').reset_index(drop=True)
    
    print(f"📊 原始数据: {len(data)} 条")
    
    # 2. 分析时间节点问题
    print("🔍 分析时间节点问题...")
    analyze_time_issues(data)
    
    # 3. 补充缺失的收盘时间点
    print("🔧 补充缺失的收盘时间点...")
    fixed_data = add_missing_closing_times(data)
    
    print(f"📊 修复后数据: {len(fixed_data)} 条")
    
    # 4. 验证修复结果
    print("✅ 验证修复结果...")
    verify_fix_results(fixed_data)
    
    # 5. 保存修复后的数据
    output_file = 'data/rb_1min_2022_2025_fixed.csv'
    fixed_data.to_csv(output_file, index=False)
    print(f"💾 修复后数据已保存: {output_file}")
    
    return fixed_data

def analyze_time_issues(data):
    """分析时间节点问题"""
    data['Time'] = data['DateTime'].dt.time
    time_counts = data['Time'].value_counts().sort_index()
    
    # 检查关键时间点
    key_times = {
        '09:00:00': '日盘开盘',
        '10:15:00': '第一节结束',
        '10:30:00': '第二节开始', 
        '11:30:00': '上午收盘',
        '13:30:00': '下午开盘',
        '14:59:00': '日盘最后一分钟',
        '15:00:00': '日盘收盘',
        '21:00:00': '夜盘开盘',
        '22:59:00': '夜盘最后一分钟',
        '23:00:00': '夜盘收盘'
    }
    
    print("   关键时间点状态:")
    for time_str, desc in key_times.items():
        t = time.fromisoformat(time_str)
        count = time_counts.get(t, 0)
        status = "✅" if count > 0 else "❌"
        print(f"   {status} {time_str} ({desc}): {count} 次")

def add_missing_closing_times(data):
    """
    添加缺失的收盘时间点
    基于最后一分钟的数据生成收盘时间点数据
    """
    fixed_data = data.copy()
    new_rows = []
    
    # 按交易日分组处理
    data['Date'] = data['DateTime'].dt.date
    
    for date, day_data in data.groupby('Date'):
        day_data = day_data.sort_values('DateTime')
        
        # 处理日盘收盘 (15:00:00)
        day_session = day_data[
            (day_data['DateTime'].dt.hour >= 9) & 
            (day_data['DateTime'].dt.hour <= 14)
        ]
        
        if len(day_session) > 0:
            # 找到14:59的数据
            last_day_minute = day_session[day_session['DateTime'].dt.time == time(14, 59)]
            if len(last_day_minute) > 0:
                last_row = last_day_minute.iloc[-1].copy()
                # 创建15:00:00的收盘数据
                closing_time = datetime.combine(date, time(15, 0))
                last_row['DateTime'] = closing_time
                # 收盘价保持不变，其他价格也保持不变（表示最后一秒的状态）
                new_rows.append(last_row)
        
        # 处理夜盘收盘 (23:00:00)
        night_session = day_data[
            (day_data['DateTime'].dt.hour >= 21) | 
            (day_data['DateTime'].dt.hour <= 2)
        ]
        
        if len(night_session) > 0:
            # 找到22:59的数据
            last_night_minute = night_session[night_session['DateTime'].dt.time == time(22, 59)]
            if len(last_night_minute) > 0:
                last_row = last_night_minute.iloc[-1].copy()
                # 创建23:00:00的收盘数据
                closing_time = datetime.combine(date, time(23, 0))
                last_row['DateTime'] = closing_time
                new_rows.append(last_row)
    
    # 添加新行到数据中
    if new_rows:
        new_df = pd.DataFrame(new_rows)
        fixed_data = pd.concat([fixed_data, new_df], ignore_index=True)
        fixed_data = fixed_data.sort_values('DateTime').reset_index(drop=True)
        print(f"   ✅ 添加了 {len(new_rows)} 个收盘时间点")
    
    return fixed_data

def verify_fix_results(data):
    """验证修复结果"""
    data['Time'] = data['DateTime'].dt.time
    time_counts = data['Time'].value_counts().sort_index()
    
    # 检查关键收盘时间点
    closing_times = {
        '15:00:00': '日盘收盘',
        '23:00:00': '夜盘收盘'
    }
    
    print("   修复后关键时间点:")
    for time_str, desc in closing_times.items():
        t = time.fromisoformat(time_str)
        count = time_counts.get(t, 0)
        status = "✅" if count > 0 else "❌"
        print(f"   {status} {time_str} ({desc}): {count} 次")
    
    # 验证数据连续性
    print("   数据连续性检查:")
    
    # 检查每日是否都有开盘和收盘数据
    data['Date'] = data['DateTime'].dt.date
    dates_with_day_open = data[data['DateTime'].dt.time == time(9, 0)]['Date'].nunique()
    dates_with_day_close = data[data['DateTime'].dt.time == time(15, 0)]['Date'].nunique()
    dates_with_night_open = data[data['DateTime'].dt.time == time(21, 0)]['Date'].nunique()
    dates_with_night_close = data[data['DateTime'].dt.time == time(23, 0)]['Date'].nunique()
    
    print(f"   日盘开盘(09:00): {dates_with_day_open} 天")
    print(f"   日盘收盘(15:00): {dates_with_day_close} 天")
    print(f"   夜盘开盘(21:00): {dates_with_night_open} 天")
    print(f"   夜盘收盘(23:00): {dates_with_night_close} 天")

def main():
    """主函数"""
    print("🔧 螺纹钢1分钟K线数据时间节点修复工具")
    print("🎯 目标: 补充缺失的收盘时间点，完善数据结构")
    print("=" * 60)
    
    try:
        fixed_data = fix_rb_1min_data()
        
        print("\n" + "=" * 60)
        print("✅ 数据修复完成!")
        print("🎯 现在数据包含完整的开盘和收盘时间点")
        print("💡 可用于更精确的实盘一致性回测")
        
    except Exception as e:
        print(f"❌ 修复失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
