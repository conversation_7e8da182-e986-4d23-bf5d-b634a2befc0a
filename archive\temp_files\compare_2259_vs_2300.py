#!/usr/bin/env python3
"""
对比本地22:59与AkShare 23:00数据
验证是否为同一分钟的K线数据
"""

import sys
sys.path.append('src')
import akshare as ak
import pandas as pd
from datetime import time, date

def compare_data():
    """对比数据"""
    print('🔍 对比本地22:59与AkShare 23:00数据...')
    
    # 1. 获取AkShare 7月31日23:00数据
    try:
        akshare_data = ak.futures_zh_minute_sina(symbol='RB0', period='1')
        akshare_data['datetime'] = pd.to_datetime(akshare_data['datetime'])
        
        target_date = date(2025, 7, 31)
        july_31_akshare = akshare_data[akshare_data['datetime'].dt.date == target_date]
        akshare_2300 = july_31_akshare[july_31_akshare['datetime'].dt.time == time(23, 0)]
        
        if len(akshare_2300) > 0:
            ak_row = akshare_2300.iloc[0]
            print('✅ AkShare 23:00数据:')
            print(f'   时间: {ak_row["datetime"]}')
            print(f'   开盘: {ak_row["open"]} 高: {ak_row["high"]} 低: {ak_row["low"]} 收盘: {ak_row["close"]}')
            print(f'   成交量: {ak_row["volume"]} 持仓: {ak_row["hold"]}')
        else:
            print('❌ AkShare未找到7月31日23:00数据')
            ak_row = None
            
    except Exception as e:
        print(f'❌ 获取AkShare数据失败: {str(e)}')
        ak_row = None

    print()

    # 2. 获取本地22:59数据
    try:
        local_data = pd.read_csv('data/rb_1min_akshare_format.csv')
        local_data['DateTime'] = pd.to_datetime(local_data['DateTime'])
        
        july_31_local = local_data[local_data['DateTime'].dt.date == target_date]
        local_2259 = july_31_local[july_31_local['DateTime'].dt.time == time(22, 59)]
        
        if len(local_2259) > 0:
            local_row = local_2259.iloc[0]
            print('✅ 本地22:59数据:')
            print(f'   时间: {local_row["DateTime"]}')
            print(f'   开盘: {local_row["Open"]} 高: {local_row["High"]} 低: {local_row["Low"]} 收盘: {local_row["Close"]}')
            print(f'   成交量: {local_row["Volume"]} 持仓: {local_row["OpenInterest"]}')
        else:
            print('❌ 本地未找到7月31日22:59数据')
            local_row = None
            
    except Exception as e:
        print(f'❌ 读取本地数据失败: {str(e)}')
        local_row = None

    print()

    # 3. 对比数据
    if ak_row is not None and local_row is not None:
        print('🔍 数据对比结果:')
        print('-' * 50)
        
        # 对比OHLC
        fields = [
            ('开盘', 'open', 'Open'), 
            ('最高', 'high', 'High'), 
            ('最低', 'low', 'Low'), 
            ('收盘', 'close', 'Close')
        ]
        
        all_match = True
        for name, ak_field, local_field in fields:
            ak_val = float(ak_row[ak_field])
            local_val = float(local_row[local_field])
            match = abs(ak_val - local_val) < 0.01
            status = '✅' if match else '❌'
            print(f'   {status} {name}: AkShare={ak_val} vs 本地={local_val}')
            if not match:
                all_match = False
        
        # 对比成交量和持仓
        ak_vol = int(ak_row['volume'])
        local_vol = int(local_row['Volume'])
        vol_match = ak_vol == local_vol
        
        ak_hold = int(ak_row['hold'])
        local_hold = int(local_row['OpenInterest'])
        hold_match = ak_hold == local_hold
        
        vol_status = '✅' if vol_match else '❌'
        hold_status = '✅' if hold_match else '❌'
        print(f'   {vol_status} 成交量: AkShare={ak_vol} vs 本地={local_vol}')
        print(f'   {hold_status} 持仓量: AkShare={ak_hold} vs 本地={local_hold}')
        
        print()
        if all_match and vol_match and hold_match:
            print('🎉 完全匹配! 本地22:59数据 = AkShare 23:00数据')
            print('💡 结论: AkShare的23:00实际上是22:59-23:00这一分钟的K线数据')
            print('🔧 解决方案: 将本地22:59数据复制为23:00数据')
            return True
        else:
            print('⚠️ 数据不匹配，需要进一步检查')
            return False
    else:
        print('❌ 无法进行对比，缺少必要数据')
        return False

if __name__ == "__main__":
    compare_data()
