#!/usr/bin/env python3
"""
快速测试实盘交易系统
"""

import sys
sys.path.append('src')

import pandas as pd
from datetime import datetime
from intraday_ai_strategy import IntradayAIStrategy
from predictive_ai_strategy import PredictiveAIStrategy

def test_intraday_strategy():
    """测试盘中实时策略"""
    print("🧪 测试盘中实时策略...")
    
    try:
        # 初始化策略
        strategy = IntradayAIStrategy()
        
        # 加载历史数据更新MA30
        data = pd.read_csv('data/real_rebar_20250729.csv')
        strategy.update_daily_ma30(data)
        
        # 模拟盘中信号
        current_price = 3500
        current_time = datetime.now()
        prev_price = 3480
        
        signal = strategy.get_intraday_signal(current_price, current_time, prev_price)
        
        print(f"   📊 当前价格: {current_price}")
        print(f"   📈 昨日MA30: {strategy.daily_ma30:.2f}")
        print(f"   🎯 交易信号: {signal['action']}")
        print(f"   📝 信号原因: {signal['reason']}")
        print("   ✅ 盘中实时策略测试通过")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 盘中实时策略测试失败: {str(e)}")
        return False

def test_predictive_strategy():
    """测试预测式策略"""
    print("\n🧪 测试预测式策略...")
    
    try:
        # 初始化策略
        strategy = PredictiveAIStrategy()
        
        # 加载历史数据
        data = pd.read_csv('data/real_rebar_20250729.csv')
        
        # 生成交易信号
        signals = strategy.generate_next_day_signals(data)
        
        print(f"   📋 生成信号数量: {len(signals)}")
        
        # 测试信号触发检查
        current_price = 3500
        current_positions = {'long': 0, 'short': 0}
        
        triggered = strategy.check_signal_triggers(current_price, current_positions)
        
        print(f"   🚨 触发信号数量: {len(triggered)}")
        print("   ✅ 预测式策略测试通过")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 预测式策略测试失败: {str(e)}")
        return False

def test_data_provider():
    """测试数据提供器"""
    print("\n🧪 测试数据提供器...")
    
    try:
        from akshare_data_provider import AkShareDataProvider
        
        provider = AkShareDataProvider()
        
        # 测试连接
        connection_ok = provider.test_connection()
        print(f"   📡 连接状态: {'✅正常' if connection_ok else '❌异常'}")
        
        # 测试获取历史数据
        try:
            data = provider.get_rebar_data(days=5)
            if data is not None and len(data) > 0:
                print(f"   📊 历史数据: ✅获取成功 ({len(data)}条)")
            else:
                print("   📊 历史数据: ⚠️获取为空")
        except:
            print("   📊 历史数据: ❌获取失败")
        
        # 测试获取实时数据
        try:
            price = provider.get_real_time_price()
            if price is not None:
                print(f"   💰 实时价格: ✅获取成功 ({price:.0f})")
            else:
                print("   💰 实时价格: ⚠️获取为空")
        except:
            print("   💰 实时价格: ❌获取失败")
        
        print("   ✅ 数据提供器测试完成")
        return True
        
    except Exception as e:
        print(f"   ❌ 数据提供器测试失败: {str(e)}")
        return False

def test_ai_models():
    """测试AI模型"""
    print("\n🧪 测试AI模型...")
    
    try:
        from ai_realtime_strategy import AIRealtimeStrategy
        
        strategy = AIRealtimeStrategy()
        
        # 加载历史数据
        data = pd.read_csv('data/real_rebar_20250729.csv')
        data = strategy.calculate_technical_indicators(data)
        
        # 测试假突破识别
        test_index = len(data) - 10
        prob = strategy.predict_false_breakout(data, test_index, 'up')
        
        print(f"   🧠 假突破识别: ✅正常 (概率: {prob:.2f})")
        
        # 测试持仓动作预测
        action = strategy.predict_position_action(data, test_index, 3500, 5, 100, -50, 'long')
        
        print(f"   🎯 止损止盈: ✅正常 (动作: {action})")
        print("   ✅ AI模型测试通过")
        
        return True
        
    except Exception as e:
        print(f"   ❌ AI模型测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🧪 实盘交易系统全面测试")
    print("=" * 50)
    
    tests = [
        ("AI模型", test_ai_models),
        ("数据提供器", test_data_provider),
        ("盘中实时策略", test_intraday_strategy),
        ("预测式策略", test_predictive_strategy),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n🔍 开始测试: {test_name}")
        results[test_name] = test_func()
    
    # 总结
    print("\n" + "=" * 50)
    print("📋 测试结果总结:")
    
    all_passed = True
    for test_name, result in results.items():
        status = "✅通过" if result else "❌失败"
        print(f"   {status} {test_name}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 所有测试通过！系统可以启动实盘交易")
        print("💡 运行 'python start_live_trading.py' 启动系统")
    else:
        print("⚠️ 部分测试失败，请检查相关组件")
        print("💡 建议先解决问题再启动实盘交易")

if __name__ == "__main__":
    main()
