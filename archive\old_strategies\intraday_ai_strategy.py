#!/usr/bin/env python3
"""
盘中实时AI交易策略
解决15:30才能决策的问题，实现盘中实时交易
"""

import pandas as pd
import numpy as np
from datetime import datetime, time
import pickle
import os
from ai_realtime_strategy import AIRealtimeStrategy

class IntradayAIStrategy(AIRealtimeStrategy):
    """盘中实时AI策略"""
    
    def __init__(self):
        super().__init__()
        self.last_signal_time = None
        self.current_position = {'long': 0, 'short': 0}
        self.entry_prices = {'long': 0, 'short': 0}
        self.daily_ma30 = None  # 缓存昨日MA30
        self.models_loaded = True  # 标记模型是否加载

        print("🕐 盘中实时AI策略初始化")
        print("⚡ 特点: 盘中实时决策，无需等待收盘")
    
    def update_daily_ma30(self, historical_data):
        """更新昨日MA30值（开盘前计算）"""
        if len(historical_data) >= 30:
            # 使用昨日及之前29天的收盘价计算MA30
            recent_closes = historical_data['Close'].tail(30)
            self.daily_ma30 = recent_closes.mean()
            print(f"📊 昨日MA30更新: {self.daily_ma30:.2f}")
        else:
            print("⚠️ 历史数据不足30天，无法计算MA30")
    
    def calculate_intraday_ma30(self, current_price, time_weight=0.1):
        """
        计算盘中MA30估值
        使用昨日MA30 + 当日价格权重调整
        """
        if self.daily_ma30 is None:
            return current_price
        
        # 简单的权重调整：昨日MA30占90%，当日价格占10%
        intraday_ma30 = self.daily_ma30 * (1 - time_weight) + current_price * time_weight
        return intraday_ma30
    
    def check_intraday_breakout(self, current_price, prev_price=None):
        """
        检查盘中突破信号
        不依赖收盘价，使用实时价格
        """
        if self.daily_ma30 is None:
            return False, None
        
        current_ma30 = self.calculate_intraday_ma30(current_price)
        
        # 如果没有前一价格，使用昨日收盘价
        if prev_price is None:
            return False, None
        
        # 检查穿越
        if prev_price <= self.daily_ma30 and current_price > current_ma30:
            return True, 'up'
        elif prev_price >= self.daily_ma30 and current_price < current_ma30:
            return True, 'down'
        
        return False, None
    
    def get_intraday_signal(self, current_price, current_time, prev_price=None):
        """
        获取盘中交易信号
        
        Args:
            current_price: 当前价格
            current_time: 当前时间
            prev_price: 前一个价格（可选）
        
        Returns:
            dict: 交易信号
        """
        signal = {
            'timestamp': current_time,
            'price': current_price,
            'action': 'hold',  # hold, buy, sell, close_long, close_short
            'reason': '',
            'confidence': 0.0
        }
        
        # 检查交易时间（期货交易时间）
        if not self.is_trading_time(current_time):
            signal['reason'] = '非交易时间'
            return signal
        
        # 1. 检查止损止盈（优先级最高）
        stop_signal = self.check_intraday_stop_loss_profit(current_price)
        if stop_signal['action'] != 'hold':
            return stop_signal
        
        # 2. 检查开仓信号
        if self.current_position['long'] == 0 and self.current_position['short'] == 0:
            breakout, direction = self.check_intraday_breakout(current_price, prev_price)
            
            if breakout:
                # 简化的假突破检测（基于价格波动）
                is_false_breakout = self.quick_false_breakout_check(current_price, direction)
                
                if not is_false_breakout:
                    signal['action'] = 'buy' if direction == 'up' else 'sell'
                    signal['reason'] = f'MA30{direction}穿越，AI确认真突破'
                    signal['confidence'] = 0.7
                else:
                    signal['reason'] = f'MA30{direction}穿越，但AI识别为假突破'
        
        return signal
    
    def quick_false_breakout_check(self, current_price, direction):
        """
        快速假突破检测（简化版，适用于盘中）
        基于价格波动和简单规则
        """
        # 这里可以使用简化的规则或者预训练的轻量级模型
        # 暂时使用简单的波动率检测
        
        # 如果价格变化过于剧烈，可能是假突破
        if self.daily_ma30:
            price_deviation = abs(current_price - self.daily_ma30) / self.daily_ma30
            if price_deviation > 0.03:  # 偏离超过3%
                return True
        
        return False  # 默认认为是真突破
    
    def check_intraday_stop_loss_profit(self, current_price):
        """检查盘中止损止盈"""
        signal = {
            'timestamp': datetime.now(),
            'price': current_price,
            'action': 'hold',
            'reason': '',
            'confidence': 0.0
        }
        
        # 检查多仓止损止盈
        if self.current_position['long'] > 0:
            profit = current_price - self.entry_prices['long']
            
            if profit <= -30:  # 止损
                signal['action'] = 'close_long'
                signal['reason'] = f'多仓止损，亏损{profit:.0f}元/吨'
                signal['confidence'] = 0.9
            elif profit >= 50:  # 止盈
                signal['action'] = 'close_long'
                signal['reason'] = f'多仓止盈，盈利{profit:.0f}元/吨'
                signal['confidence'] = 0.8
        
        # 检查空仓止损止盈
        if self.current_position['short'] > 0:
            profit = self.entry_prices['short'] - current_price
            
            if profit <= -30:  # 止损
                signal['action'] = 'close_short'
                signal['reason'] = f'空仓止损，亏损{profit:.0f}元/吨'
                signal['confidence'] = 0.9
            elif profit >= 50:  # 止盈
                signal['action'] = 'close_short'
                signal['reason'] = f'空仓止盈，盈利{profit:.0f}元/吨'
                signal['confidence'] = 0.8
        
        return signal
    
    def is_trading_time(self, current_time):
        """检查是否为交易时间"""
        if isinstance(current_time, datetime):
            current_time = current_time.time()
        
        # 期货交易时间
        morning_start = time(9, 0)
        morning_end = time(11, 30)
        afternoon_start = time(13, 30)
        afternoon_end = time(15, 0)
        night_start = time(21, 0)
        night_end = time(23, 0)
        
        return (
            (morning_start <= current_time <= morning_end) or
            (afternoon_start <= current_time <= afternoon_end) or
            (night_start <= current_time <= night_end)
        )
    
    def execute_signal(self, signal):
        """执行交易信号"""
        action = signal['action']
        price = signal['price']
        
        if action == 'buy':
            # 双开：同时开多仓和空仓
            self.current_position['long'] = 1
            self.current_position['short'] = 1
            self.entry_prices['long'] = price
            self.entry_prices['short'] = price
            print(f"🔄 双开执行: 多仓+空仓 @ {price:.0f}")
            
        elif action == 'sell':
            # 双开：同时开多仓和空仓
            self.current_position['long'] = 1
            self.current_position['short'] = 1
            self.entry_prices['long'] = price
            self.entry_prices['short'] = price
            print(f"🔄 双开执行: 多仓+空仓 @ {price:.0f}")
            
        elif action == 'close_long':
            if self.current_position['long'] > 0:
                profit = price - self.entry_prices['long']
                print(f"📈 平多仓: {self.entry_prices['long']:.0f} → {price:.0f}, 盈亏{profit:+.0f}元/吨")
                self.current_position['long'] = 0
                
        elif action == 'close_short':
            if self.current_position['short'] > 0:
                profit = self.entry_prices['short'] - price
                print(f"📉 平空仓: {self.entry_prices['short']:.0f} → {price:.0f}, 盈亏{profit:+.0f}元/吨")
                self.current_position['short'] = 0
    
    def get_position_status(self):
        """获取当前持仓状态"""
        return {
            'long_position': self.current_position['long'],
            'short_position': self.current_position['short'],
            'long_entry_price': self.entry_prices['long'] if self.current_position['long'] > 0 else None,
            'short_entry_price': self.entry_prices['short'] if self.current_position['short'] > 0 else None,
            'daily_ma30': self.daily_ma30
        }

# 使用示例
if __name__ == "__main__":
    # 初始化策略
    strategy = IntradayAIStrategy()
    
    # 模拟开盘前更新MA30
    # historical_data = pd.read_csv('../data/real_rebar_20250729.csv')
    # strategy.update_daily_ma30(historical_data)
    
    # 模拟盘中交易
    current_time = datetime.now()
    current_price = 3500
    prev_price = 3480
    
    signal = strategy.get_intraday_signal(current_price, current_time, prev_price)
    print(f"交易信号: {signal}")
    
    if signal['action'] != 'hold':
        strategy.execute_signal(signal)
    
    print(f"持仓状态: {strategy.get_position_status()}")
