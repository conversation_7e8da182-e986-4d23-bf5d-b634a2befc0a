#!/usr/bin/env python3
"""
AI增强动态双开策略 - 主程序入口
集成AkShare数据源的AI实时交易策略
"""

import sys
import os

# 添加src目录到路径
sys.path.append('src')

from ai_realtime_akshare_strategy import AIRealtimeAkShareStrategy

def main():
    """主程序入口"""
    print("🚀 AI增强动态双开策略")
    print("=" * 50)
    print("基于AkShare数据源的AI实时交易策略")
    print("支持：实时数据获取 + 30天滑动窗口训练 + 实时交易信号")
    print()
    
    try:
        # 初始化策略
        strategy = AIRealtimeAkShareStrategy(
            training_window_years=2.5,  # 2.5年训练窗口
            update_frequency_days=30    # 30天更新频率
        )
        
        # 初始化系统
        if not strategy.initialize_system():
            print("❌ 系统初始化失败")
            return
        
        print("✅ 系统初始化成功")
        print("\n选择运行模式:")
        print("1 - 生成交易信号")
        print("2 - 交互模式")
        print("3 - 自动定时模式")
        print("4 - ⚡ 盘中实时交易模式（无未来函数）")

        choice = input("\n请选择 (1/2/3/4): ").strip()
        
        if choice == '1':
            # 生成一次交易信号
            print("\n📡 生成交易信号...")
            signals = strategy.generate_trading_signal()
            if signals:
                strategy.execute_signal_action(signals)
        
        elif choice == '2':
            # 交互模式
            strategy.run_interactive_mode()
        
        elif choice == '3':
            # 自动定时模式
            print("\n⏰ 启动自动定时模式...")
            print("系统将在每日15:30自动生成交易信号")
            strategy.start_scheduled_strategy()

        elif choice == '4':
            # 盘中实时交易模式
            print("\n⚡ 启动盘中实时交易模式...")
            print("🎯 特点: 无需等待收盘，盘中即时决策")
            strategy.start_intraday_trading()

        else:
            print("❌ 无效选择")
    
    except KeyboardInterrupt:
        print("\n👋 程序已退出")
    except Exception as e:
        print(f"❌ 程序错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
