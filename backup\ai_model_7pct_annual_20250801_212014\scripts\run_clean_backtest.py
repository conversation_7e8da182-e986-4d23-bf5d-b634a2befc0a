#!/usr/bin/env python3
"""
无未来函数的AI交易系统回测
确保所有决策都基于当前时点可获得的数据
"""

import sys
sys.path.append('src')

from ai_realtime_strategy import AIRealtimeStrategy
import pandas as pd
import numpy as np
from datetime import datetime

def run_clean_backtest():
    """运行无未来函数的回测"""
    print("🔄 运行无未来函数的AI交易系统回测")
    print("=" * 60)
    print("✅ 确保所有决策都基于当前时点可获得的数据")
    print()
    
    try:
        # 1. 加载数据
        data = pd.read_csv('data/real_rebar_20250729.csv')
        data['Date'] = pd.to_datetime(data['Date'])
        data = data.sort_values('Date').reset_index(drop=True)
        
        strategy = AIRealtimeStrategy()
        data = strategy.calculate_technical_indicators(data)
        
        print(f"📈 数据范围: {data['Date'].min().strftime('%Y-%m-%d')} 至 {data['Date'].max().strftime('%Y-%m-%d')}")
        print(f"📊 数据条数: {len(data)} 条记录")
        print()
        
        # 2. 运行无未来函数回测
        result = run_no_future_function_backtest(data, strategy)
        
        # 3. 生成报告
        generate_clean_backtest_report(result)
        
        print("✅ 无未来函数回测完成!")
        
    except Exception as e:
        print(f"❌ 回测失败: {str(e)}")
        import traceback
        traceback.print_exc()

def run_no_future_function_backtest(data, strategy):
    """运行无未来函数回测"""
    print("🔄 执行无未来函数回测...")
    
    # 初始化回测参数
    initial_balance = 100000
    balance = initial_balance
    long_position = 0
    short_position = 0
    long_entry_price = 0
    short_entry_price = 0
    long_entry_date = None
    short_entry_date = None
    
    trades = []
    ai_decisions = 0
    ai_prevented = 0
    
    # 换月日期
    rollover_dates = [
        datetime(2022, 12, 2), datetime(2023, 4, 6), datetime(2023, 9, 5),
        datetime(2023, 12, 7), datetime(2024, 4, 3), datetime(2024, 9, 3),
        datetime(2024, 12, 6), datetime(2025, 4, 8)
    ]
    
    # 回测主循环
    for i in range(100, len(data)):
        current_price = data.loc[i, 'Close']
        current_ma30 = data.loc[i, 'MA30']
        prev_price = data.loc[i-1, 'Close']
        prev_ma30 = data.loc[i-1, 'MA30']
        current_date = data.loc[i, 'Date']
        
        if pd.isna(current_ma30) or pd.isna(prev_ma30):
            continue
        
        # 检查换月强制平仓
        is_rollover = any((rollover_date - current_date).days in range(1, 4) for rollover_date in rollover_dates)
        
        if is_rollover and (long_position > 0 or short_position > 0):
            if long_position > 0:
                profit_long = current_price - long_entry_price
                balance += profit_long * 100
                trades.append(profit_long)
                long_position = 0
            
            if short_position > 0:
                profit_short = short_entry_price - current_price
                balance += profit_short * 100
                trades.append(profit_short)
                short_position = 0
            continue
        
        # 无未来函数的止损止盈检查
        if long_position > 0:
            holding_days = (current_date - long_entry_date).days
            
            # 使用修复后的决策方法 (无未来函数)
            ai_action = strategy.determine_best_decision(data, i, long_entry_price, 'long')
            
            if ai_action in [1, 2]:  # 止损或止盈
                profit = current_price - long_entry_price
                balance += profit * 100
                trades.append(profit)
                ai_decisions += 1
                long_position = 0
        
        if short_position > 0:
            holding_days = (current_date - short_entry_date).days
            
            # 使用修复后的决策方法 (无未来函数)
            ai_action = strategy.determine_best_decision(data, i, short_entry_price, 'short')
            
            if ai_action in [1, 2]:  # 止损或止盈
                profit = short_entry_price - current_price
                balance += profit * 100
                trades.append(profit)
                ai_decisions += 1
                short_position = 0
        
        # 检查30MA穿越
        price_cross_ma30 = (
            (prev_price > prev_ma30 and current_price <= current_ma30) or
            (prev_price < prev_ma30 and current_price >= current_ma30)
        )
        
        if price_cross_ma30:
            # 确定穿越方向
            if prev_price <= prev_ma30 and current_price > current_ma30:
                breakout_type = 'up'
            else:
                breakout_type = 'down'
            
            # AI假突破识别 (只使用当前和历史数据)
            ai_prob = strategy.predict_false_breakout(data, i, breakout_type)
            
            if ai_prob > 0.5:  # AI识别为假突破
                ai_prevented += 1
            else:
                # AI认为是真突破，执行双开逻辑
                if long_position == 0 and short_position == 0:
                    long_position = 1
                    short_position = 1
                    long_entry_price = current_price
                    short_entry_price = current_price
                    long_entry_date = current_date
                    short_entry_date = current_date
    
    # 计算统计
    if trades:
        profits = trades
        total_profit = sum(profits)
        win_trades = [p for p in profits if p > 0]
        win_rate = len(win_trades) / len(profits)
        total_return = (balance - initial_balance) / initial_balance
        max_profit = max(profits)
        max_loss = min(profits)
        avg_profit = np.mean(profits)
    else:
        total_return = 0
        win_rate = 0
        max_profit = 0
        max_loss = 0
        avg_profit = 0
    
    print(f"   💰 最终资金: {balance:,.0f} 元")
    print(f"   📈 总收益率: {total_return:.2%}")
    print(f"   📊 交易次数: {len(trades)}")
    print(f"   🎯 胜率: {win_rate:.2%}")
    print(f"   📈 最大盈利: {max_profit:.0f} 元/吨")
    print(f"   📉 最大亏损: {max_loss:.0f} 元/吨")
    print(f"   🤖 AI决策: {ai_decisions} 次")
    print(f"   🛡️ AI阻止: {ai_prevented} 次")
    
    return {
        'initial_balance': initial_balance,
        'final_balance': balance,
        'total_return': total_return,
        'total_trades': len(trades),
        'win_rate': win_rate,
        'max_profit': max_profit,
        'max_loss': max_loss,
        'avg_profit': avg_profit,
        'ai_decisions': ai_decisions,
        'ai_prevented': ai_prevented,
        'trades': trades
    }

def generate_clean_backtest_report(result):
    """生成无未来函数回测报告"""
    print("\n📋 生成无未来函数回测报告...")
    
    report = []
    report.append("# 无未来函数AI交易系统回测报告")
    report.append(f"## 回测时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    report.append("")
    
    report.append("## ✅ 修复说明")
    report.append("本次回测已完全移除未来函数，确保所有决策都基于当前时点可获得的数据：")
    report.append("1. **移除未来数据**: 决策不再使用未来3天的价格数据")
    report.append("2. **基于技术指标**: 使用RSI、MA30等当前可获得的指标")
    report.append("3. **实盘可复现**: 所有逻辑都可以在实盘中实现")
    report.append("")
    
    report.append("## 📊 回测结果")
    report.append(f"- **初始资金**: {result['initial_balance']:,.0f} 元")
    report.append(f"- **最终资金**: {result['final_balance']:,.0f} 元")
    report.append(f"- **总收益率**: {result['total_return']:.2%}")
    report.append(f"- **年化收益率**: {result['total_return'] / 2.5:.2%} (约)")
    report.append(f"- **总交易次数**: {result['total_trades']} 次")
    report.append(f"- **胜率**: {result['win_rate']:.2%}")
    report.append(f"- **平均盈亏**: {result['avg_profit']:.0f} 元/吨")
    report.append(f"- **最大盈利**: {result['max_profit']:.0f} 元/吨")
    report.append(f"- **最大亏损**: {result['max_loss']:.0f} 元/吨")
    report.append("")
    
    report.append("## 🤖 AI决策统计")
    report.append(f"- **AI止损止盈决策**: {result['ai_decisions']} 次")
    report.append(f"- **AI阻止假突破**: {result['ai_prevented']} 次")
    report.append(f"- **决策基础**: 技术指标 + 历史数据")
    report.append("")
    
    report.append("## 🔧 修复对比")
    report.append("| 项目 | 修复前 | 修复后 | 说明 |")
    report.append("|------|--------|--------|------|")
    report.append("| 决策依据 | 未来3天数据 | 当前技术指标 | 移除未来函数 |")
    report.append("| 止盈逻辑 | 预测未来回撤 | RSI超买+MA偏离 | 实盘可用 |")
    report.append("| 止损逻辑 | 预测未来难回本 | 固定止损+RSI | 风险控制 |")
    report.append("| 收益率 | 147.40% | 待验证 | 真实表现 |")
    report.append("")
    
    report.append("## ⚠️ 重要说明")
    report.append("1. **真实性**: 本次回测结果更接近实盘表现")
    report.append("2. **可复现**: 所有逻辑都可以在实盘中实现")
    report.append("3. **保守性**: 收益率可能低于之前的虚高结果")
    report.append("4. **可靠性**: 风险控制指标更加可信")
    
    # 保存报告
    with open('results/clean_backtest_report.md', 'w', encoding='utf-8') as f:
        for line in report:
            f.write(line + '\n')
    
    print("   💾 无未来函数回测报告已保存: results/clean_backtest_report.md")

if __name__ == "__main__":
    run_clean_backtest()
