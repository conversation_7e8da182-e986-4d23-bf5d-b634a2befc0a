#!/usr/bin/env python3
"""
项目结构整理和清理脚本
清理重复、临时和测试文件，整理项目结构
"""

import os
import shutil
from datetime import datetime

def cleanup_project():
    """清理项目结构"""
    print("🧹 开始项目结构整理...")
    print("=" * 60)
    
    # 1. 创建清理目录
    create_cleanup_directories()
    
    # 2. 移动临时和测试文件
    move_temporary_files()
    
    # 3. 移动数据处理文件
    move_data_processing_files()
    
    # 4. 清理重复的策略文件
    cleanup_duplicate_strategies()
    
    # 5. 整理文档
    organize_documentation()
    
    # 6. 生成清理报告
    generate_cleanup_report()
    
    print("\n✅ 项目结构整理完成!")

def create_cleanup_directories():
    """创建清理用的目录结构"""
    print("📁 创建目录结构...")
    
    directories = [
        'archive',
        'archive/temp_files',
        'archive/test_files', 
        'archive/data_processing',
        'archive/old_strategies',
        'tools',
        'tools/data_processing',
        'tools/analysis'
    ]
    
    for dir_path in directories:
        if not os.path.exists(dir_path):
            os.makedirs(dir_path)
            print(f"   ✅ 创建目录: {dir_path}")

def move_temporary_files():
    """移动临时文件"""
    print("\n🗂️ 移动临时文件...")
    
    temp_files = [
        'adjust_time_plus_1min.py',
        'compare_2259_vs_2300.py', 
        'correct_rb_data_format.py',
        'fix_rb_1min_data.py',
        'process_rb_1min_data.py'
    ]
    
    for file in temp_files:
        if os.path.exists(file):
            shutil.move(file, f'archive/temp_files/{file}')
            print(f"   📦 移动: {file} → archive/temp_files/")

def move_data_processing_files():
    """移动数据处理文件"""
    print("\n📊 移动数据处理文件...")
    
    data_files = [
        'analyze_yearly_returns.py'
    ]
    
    for file in data_files:
        if os.path.exists(file):
            shutil.move(file, f'tools/analysis/{file}')
            print(f"   📊 移动: {file} → tools/analysis/")

def cleanup_duplicate_strategies():
    """清理重复的策略文件"""
    print("\n🔄 清理重复策略文件...")
    
    # 检查src目录中的策略文件
    strategy_files = [
        'src/ai_realtime_akshare_strategy.py',
        'src/intraday_ai_strategy.py', 
        'src/predictive_ai_strategy.py'
    ]
    
    for file in strategy_files:
        if os.path.exists(file):
            filename = os.path.basename(file)
            shutil.move(file, f'archive/old_strategies/{filename}')
            print(f"   🗃️ 归档: {file} → archive/old_strategies/")

def organize_documentation():
    """整理文档"""
    print("\n📚 整理文档...")
    
    # 检查是否需要移动或整理文档
    doc_files = [
        'DATA_SOURCE_ANALYSIS.md',
        'PROJECT_STRUCTURE.md'
    ]
    
    for file in doc_files:
        if os.path.exists(file):
            if not os.path.exists(f'docs/{file}'):
                shutil.move(file, f'docs/{file}')
                print(f"   📄 移动: {file} → docs/")

def move_test_files():
    """移动测试文件"""
    print("\n🧪 移动测试文件...")
    
    test_files = [
        'test_1min_data.py',
        'test_breakout_logic.py',
        'test_conditional_orders.py',
        'test_data_source.py',
        'test_intraday_trading.py',
        'test_live_system.py',
        'run_simulation_test.py',
        'simulate_live_trading.py'
    ]
    
    for file in test_files:
        if os.path.exists(file):
            shutil.move(file, f'archive/test_files/{file}')
            print(f"   🧪 移动: {file} → archive/test_files/")

def generate_cleanup_report():
    """生成清理报告"""
    print("\n📋 生成清理报告...")
    
    report = [
        "# 项目结构整理报告",
        f"## 整理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
        "",
        "## 整理后的项目结构",
        "",
        "### 核心文件",
        "- `main.py` - 主程序入口",
        "- `run_backtest.py` - 回测引擎",
        "- `live_trading_main.py` - 实盘交易主程序",
        "- `start_live_trading.py` - 实盘交易启动脚本",
        "",
        "### 核心模块 (src/)",
        "- `ai_realtime_strategy.py` - 核心AI策略模块",
        "- `akshare_data_provider.py` - 数据获取模块",
        "",
        "### 数据文件 (data/)",
        "- `real_rebar_20250729.csv` - 历史日K数据",
        "- `rb.txt` - 1分钟K线原始数据",
        "",
        "### 模型文件 (models/)",
        "- `breakout_models_20250729.pkl` - 假突破预测模型",
        "- `stop_models_20250729.pkl` - 止损止盈决策模型",
        "",
        "### 结果文件 (results/)",
        "- 各种回测报告和交易计划",
        "",
        "### 文档 (docs/)",
        "- 项目文档和使用指南",
        "",
        "### 工具 (tools/)",
        "- `analysis/` - 分析工具",
        "- `data_processing/` - 数据处理工具",
        "",
        "### 归档 (archive/)",
        "- `temp_files/` - 临时文件",
        "- `test_files/` - 测试文件", 
        "- `old_strategies/` - 旧版策略文件",
        "",
        "### 备份 (backup/)",
        "- 模型和配置备份",
        "",
        "## 清理内容",
        "",
        "### 移动到归档的文件",
        "- 临时数据处理脚本",
        "- 测试和验证脚本",
        "- 重复的策略文件",
        "",
        "### 保留的核心文件",
        "- 主要业务逻辑文件",
        "- 配置和文档文件",
        "- 数据和模型文件",
        "",
        "## 使用说明",
        "",
        "### 运行回测",
        "```bash",
        "python run_backtest.py",
        "```",
        "",
        "### 启动实盘交易",
        "```bash", 
        "python start_live_trading.py",
        "```",
        "",
        "### 分析工具",
        "```bash",
        "python tools/analysis/analyze_yearly_returns.py",
        "```",
        "",
        "✅ **项目结构已优化，代码更加清晰和专业**"
    ]
    
    with open('PROJECT_CLEANUP_REPORT.md', 'w', encoding='utf-8') as f:
        f.write('\n'.join(report))
    
    print(f"   📄 生成报告: PROJECT_CLEANUP_REPORT.md")

def main():
    """主函数"""
    print("🧹 项目结构整理工具")
    print("🎯 目标: 清理重复和临时文件，优化项目结构")
    print()
    
    # 确认操作
    response = input("确认开始整理项目结构? (y/N): ").strip().lower()
    if response != 'y':
        print("❌ 操作已取消")
        return
    
    cleanup_project()

if __name__ == "__main__":
    main()
