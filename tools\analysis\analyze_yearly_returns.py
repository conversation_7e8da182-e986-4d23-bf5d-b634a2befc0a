#!/usr/bin/env python3
"""
分析回测的各年度收益
"""

import sys
sys.path.append('src')

import pandas as pd
import numpy as np
from datetime import datetime
from ai_realtime_strategy import AIRealtimeStrategy

def analyze_yearly_returns():
    """分析各年度收益"""
    print("📊 分析各年度收益...")
    print("=" * 60)
    
    try:
        # 1. 加载数据和策略
        data = pd.read_csv('data/real_rebar_20250729.csv')
        data['Date'] = pd.to_datetime(data['Date'])
        data = data.sort_values('Date').reset_index(drop=True)
        
        strategy = AIRealtimeStrategy()
        if not strategy.load_latest_models():
            print("❌ 模型加载失败")
            return
        
        data = strategy.calculate_technical_indicators(data)
        
        print(f"📈 数据范围: {data['Date'].min().strftime('%Y-%m-%d')} 至 {data['Date'].max().strftime('%Y-%m-%d')}")
        print(f"📊 数据条数: {len(data)} 条记录")
        print()
        
        # 2. 运行回测并记录每笔交易的时间
        yearly_trades = run_backtest_with_yearly_tracking(data, strategy)
        
        # 3. 分析各年度收益
        analyze_yearly_performance(yearly_trades)
        
    except Exception as e:
        print(f"❌ 分析失败: {str(e)}")
        import traceback
        traceback.print_exc()

def run_backtest_with_yearly_tracking(data, strategy):
    """运行回测并跟踪各年度交易"""
    print("🔄 运行回测并跟踪年度交易...")
    
    # 初始化回测参数
    initial_balance = 100000
    balance = initial_balance
    long_position = 0
    short_position = 0
    long_entry_price = 0
    short_entry_price = 0
    long_entry_date = None
    short_entry_date = None
    
    yearly_trades = {}  # 按年度记录交易
    
    # 换月日期
    rollover_dates = [
        datetime(2022, 12, 2), datetime(2023, 4, 6), datetime(2023, 9, 5),
        datetime(2023, 12, 7), datetime(2024, 4, 3), datetime(2024, 9, 3),
        datetime(2024, 12, 6), datetime(2025, 4, 8)
    ]
    
    # 回测主循环
    for i in range(100, len(data)):
        current_price = data.loc[i, 'Close']
        current_date = data.loc[i, 'Date']
        current_year = current_date.year
        
        if current_year not in yearly_trades:
            yearly_trades[current_year] = {
                'trades': [],
                'start_balance': balance,
                'end_balance': balance
            }
        
        # 检查换月强制平仓
        is_rollover = any((rollover_date - current_date).days in range(1, 4) for rollover_date in rollover_dates)
        
        if is_rollover and (long_position > 0 or short_position > 0):
            if long_position > 0:
                profit_long = current_price - long_entry_price
                profit_amount = profit_long * 10
                balance += profit_amount
                yearly_trades[current_year]['trades'].append({
                    'type': 'long_rollover',
                    'entry_price': long_entry_price,
                    'exit_price': current_price,
                    'profit_per_ton': profit_long,
                    'profit_amount': profit_amount,
                    'entry_date': long_entry_date,
                    'exit_date': current_date,
                    'reason': '换月平仓'
                })
                long_position = 0
            
            if short_position > 0:
                profit_short = short_entry_price - current_price
                profit_amount = profit_short * 10
                balance += profit_amount
                yearly_trades[current_year]['trades'].append({
                    'type': 'short_rollover',
                    'entry_price': short_entry_price,
                    'exit_price': current_price,
                    'profit_per_ton': profit_short,
                    'profit_amount': profit_amount,
                    'entry_date': short_entry_date,
                    'exit_date': current_date,
                    'reason': '换月平仓'
                })
                short_position = 0
            continue
        
        # 检查MA30穿越
        if i > 100:
            prev_price = data.loc[i-1, 'Close']
            prev_ma30 = data.loc[i-1, 'MA30']
            current_ma30 = data.loc[i, 'MA30']
            
            price_cross_ma30 = (
                (prev_price > prev_ma30 and current_price <= current_ma30) or
                (prev_price < prev_ma30 and current_price >= current_ma30)
            )
            
            if price_cross_ma30:
                # 确定穿越方向
                if prev_price <= prev_ma30 and current_price > current_ma30:
                    breakout_type = 'up'
                else:
                    breakout_type = 'down'
                
                # AI假突破检测
                ai_prob = strategy.predict_false_breakout(data, i, breakout_type)
                
                if ai_prob <= 0.5 and long_position == 0 and short_position == 0:
                    # 双开锁仓
                    long_position = 1
                    short_position = 1
                    long_entry_price = current_price
                    short_entry_price = current_price
                    long_entry_date = current_date
                    short_entry_date = current_date
        
        # AI智能止损止盈
        if long_position > 0:
            decision = strategy.determine_best_decision(data, i, long_entry_price, 'long')

            if decision == 1:  # 止损
                profit_long = current_price - long_entry_price
                profit_amount = profit_long * 10
                balance += profit_amount
                yearly_trades[current_year]['trades'].append({
                    'type': 'long_close',
                    'entry_price': long_entry_price,
                    'exit_price': current_price,
                    'profit_per_ton': profit_long,
                    'profit_amount': profit_amount,
                    'entry_date': long_entry_date,
                    'exit_date': current_date,
                    'reason': 'AI止损'
                })
                long_position = 0
            elif decision == 2:  # 止盈
                profit_long = current_price - long_entry_price
                profit_amount = profit_long * 10
                balance += profit_amount
                yearly_trades[current_year]['trades'].append({
                    'type': 'long_close',
                    'entry_price': long_entry_price,
                    'exit_price': current_price,
                    'profit_per_ton': profit_long,
                    'profit_amount': profit_amount,
                    'entry_date': long_entry_date,
                    'exit_date': current_date,
                    'reason': 'AI止盈'
                })
                long_position = 0

        if short_position > 0:
            decision = strategy.determine_best_decision(data, i, short_entry_price, 'short')

            if decision == 1:  # 止损
                profit_short = short_entry_price - current_price
                profit_amount = profit_short * 10
                balance += profit_amount
                yearly_trades[current_year]['trades'].append({
                    'type': 'short_close',
                    'entry_price': short_entry_price,
                    'exit_price': current_price,
                    'profit_per_ton': profit_short,
                    'profit_amount': profit_amount,
                    'entry_date': short_entry_date,
                    'exit_date': current_date,
                    'reason': 'AI止损'
                })
                short_position = 0
            elif decision == 2:  # 止盈
                profit_short = short_entry_price - current_price
                profit_amount = profit_short * 10
                balance += profit_amount
                yearly_trades[current_year]['trades'].append({
                    'type': 'short_close',
                    'entry_price': short_entry_price,
                    'exit_price': current_price,
                    'profit_per_ton': profit_short,
                    'profit_amount': profit_amount,
                    'entry_date': short_entry_date,
                    'exit_date': current_date,
                    'reason': 'AI止盈'
                })
                short_position = 0
        
        # 更新年度结束余额
        yearly_trades[current_year]['end_balance'] = balance
    
    # 最终平仓
    if long_position > 0 or short_position > 0:
        final_price = data.iloc[-1]['Close']
        final_date = data.iloc[-1]['Date']
        final_year = final_date.year
        
        if long_position > 0:
            profit_long = final_price - long_entry_price
            profit_amount = profit_long * 10
            balance += profit_amount
            yearly_trades[final_year]['trades'].append({
                'type': 'long_final',
                'entry_price': long_entry_price,
                'exit_price': final_price,
                'profit_per_ton': profit_long,
                'profit_amount': profit_amount,
                'entry_date': long_entry_date,
                'exit_date': final_date,
                'reason': '最终平仓'
            })
        
        if short_position > 0:
            profit_short = short_entry_price - final_price
            profit_amount = profit_short * 10
            balance += profit_amount
            yearly_trades[final_year]['trades'].append({
                'type': 'short_final',
                'entry_price': short_entry_price,
                'exit_price': final_price,
                'profit_per_ton': profit_short,
                'profit_amount': profit_amount,
                'entry_date': short_entry_date,
                'exit_date': final_date,
                'reason': '最终平仓'
            })
        
        yearly_trades[final_year]['end_balance'] = balance
    
    print(f"   ✅ 回测完成，最终资金: {balance:,.0f} 元")
    return yearly_trades

def analyze_yearly_performance(yearly_trades):
    """分析各年度表现"""
    print("\n📊 各年度收益分析:")
    print("=" * 80)
    
    total_initial = 100000
    cumulative_balance = total_initial
    
    for year in sorted(yearly_trades.keys()):
        year_data = yearly_trades[year]
        
        # 计算年度收益
        year_start = year_data.get('start_balance', cumulative_balance)
        year_end = year_data['end_balance']
        year_return = (year_end - year_start) / year_start
        year_profit = year_end - year_start
        
        # 统计交易
        trades = year_data['trades']
        trade_count = len(trades)
        
        if trades:
            profits = [t['profit_amount'] for t in trades]
            win_trades = [p for p in profits if p > 0]
            win_rate = len(win_trades) / len(profits) if profits else 0
            total_profit = sum(profits)
            avg_profit = np.mean(profits)
            max_profit = max(profits)
            max_loss = min(profits)
        else:
            win_rate = 0
            total_profit = 0
            avg_profit = 0
            max_profit = 0
            max_loss = 0
        
        print(f"\n📅 {year}年:")
        print(f"   💰 期初资金: {year_start:,.0f} 元")
        print(f"   💰 期末资金: {year_end:,.0f} 元")
        print(f"   📈 年度收益: {year_profit:+,.0f} 元 ({year_return:+.2%})")
        print(f"   📊 交易次数: {trade_count} 次")
        if trade_count > 0:
            print(f"   🎯 胜率: {win_rate:.1%}")
            print(f"   📊 平均盈亏: {avg_profit:+.0f} 元/次")
            print(f"   📈 最大盈利: {max_profit:+.0f} 元")
            print(f"   📉 最大亏损: {max_loss:+.0f} 元")
        
        cumulative_balance = year_end
    
    # 总体统计
    total_return = (cumulative_balance - total_initial) / total_initial
    years_count = len(yearly_trades)
    annual_return = (1 + total_return) ** (1/years_count) - 1 if years_count > 0 else 0
    
    print(f"\n📊 总体表现:")
    print(f"   💰 初始资金: {total_initial:,.0f} 元")
    print(f"   💰 最终资金: {cumulative_balance:,.0f} 元")
    print(f"   📈 总收益率: {total_return:.2%}")
    print(f"   📊 年化收益率: {annual_return:.2%}")
    print(f"   📅 交易年数: {years_count} 年")

def main():
    """主函数"""
    print("📊 各年度收益分析工具")
    print("🎯 目标: 分析当前回测策略的各年度表现")
    print()
    
    analyze_yearly_returns()

if __name__ == "__main__":
    main()
