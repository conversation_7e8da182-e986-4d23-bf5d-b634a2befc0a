#!/usr/bin/env python3
"""
根据AkShare格式修正本地数据
确保与AkShare完全一致
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta, time, date

def correct_rb_data_format():
    """
    根据AkShare格式修正本地数据
    """
    print("🔧 根据AkShare格式修正本地数据...")
    
    # 1. 读取原始本地数据（未修复的版本）
    try:
        # 重新从rb.txt提取，但这次按AkShare格式处理
        print("📂 重新处理rb.txt数据...")
        data = pd.read_csv('data/rb.txt', encoding='gbk')
        data['时间'] = pd.to_datetime(data['时间'])
        data = data.sort_values('时间').reset_index(drop=True)
        
        print(f"   原始数据: {len(data):,} 条")
        
        # 2. 筛选目标时间范围
        target_start = datetime(2022, 8, 1)
        target_end = datetime(2025, 7, 29, 23, 59, 59)
        
        # 处理夜盘归属
        data['交易日'] = data['时间'].apply(get_trading_date)
        
        # 筛选交易日在目标范围内的数据
        filtered_data = data[
            (data['交易日'] >= target_start.date()) & 
            (data['交易日'] <= target_end.date())
        ].copy()
        
        print(f"   筛选后: {len(filtered_data):,} 条")
        
        # 3. 按AkShare格式调整时间点
        print("🔧 按AkShare格式调整时间点...")
        corrected_data = adjust_time_points_to_akshare_format(filtered_data)
        
        # 4. 重命名列以匹配AkShare格式
        corrected_data = corrected_data.rename(columns={
            '时间': 'DateTime',
            '开盘价': 'Open',
            '最高价': 'High', 
            '最低价': 'Low',
            '收盘价': 'Close',
            '成交量': 'Volume',
            '持仓量': 'OpenInterest',
            '结算价': 'Settlement'
        })
        
        # 5. 验证与AkShare的一致性
        print("✅ 验证与AkShare的一致性...")
        verify_akshare_consistency(corrected_data)
        
        # 6. 保存修正后的数据
        output_file = 'data/rb_1min_akshare_format.csv'
        corrected_data.to_csv(output_file, index=False, encoding='utf-8')
        print(f"💾 修正后数据已保存: {output_file}")
        
        return corrected_data
        
    except Exception as e:
        print(f"❌ 处理失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def get_trading_date(dt):
    """
    根据时间确定交易日（与之前相同的逻辑）
    """
    if dt.hour >= 21:  # 夜盘时间
        next_day = dt.date() + timedelta(days=1)
        while next_day.weekday() >= 5:
            next_day += timedelta(days=1)
        return next_day
    elif dt.hour < 3:
        current_day = dt.date()
        while current_day.weekday() >= 5:
            current_day += timedelta(days=1)
        return current_day
    else:
        current_day = dt.date()
        while current_day.weekday() >= 5:
            current_day += timedelta(days=1)
        return current_day

def adjust_time_points_to_akshare_format(data):
    """
    调整时间点以匹配AkShare格式
    """
    print("   调整时间点规则:")
    print("   - 移除09:00:00，保留09:01:00开始")
    print("   - 移除15:00:00，保持14:59:00结束") 
    print("   - 保留23:00:00夜盘收盘")
    
    # 移除不符合AkShare格式的时间点
    original_count = len(data)
    
    # 移除09:00:00（AkShare从09:01:00开始）
    data = data[~((data['时间'].dt.hour == 9) & (data['时间'].dt.minute == 0))]
    
    # 移除15:00:00（AkShare在14:59:00结束日盘）
    data = data[~((data['时间'].dt.hour == 15) & (data['时间'].dt.minute == 0))]
    
    # 保留23:00:00（AkShare确实有夜盘收盘）
    # 不需要移除23:00:00
    
    removed_count = original_count - len(data)
    print(f"   移除了 {removed_count} 条不符合AkShare格式的记录")
    
    return data.reset_index(drop=True)

def verify_akshare_consistency(data):
    """
    验证与AkShare的一致性
    """
    print("   检查关键时间点:")
    
    data['Time'] = data['DateTime'].dt.time
    time_counts = data['Time'].value_counts().sort_index()
    
    # AkShare应该有的时间点
    should_have = ['09:01:00', '14:59:00', '21:00:00', '22:59:00', '23:00:00']
    # AkShare不应该有的时间点  
    should_not_have = ['09:00:00', '15:00:00']
    
    for time_str in should_have:
        t = time.fromisoformat(time_str)
        count = time_counts.get(t, 0)
        status = "✅" if count > 0 else "❌"
        print(f"   {status} {time_str}: {count} 次 (应该有)")
    
    for time_str in should_not_have:
        t = time.fromisoformat(time_str)
        count = time_counts.get(t, 0)
        status = "✅" if count == 0 else "❌"
        print(f"   {status} {time_str}: {count} 次 (不应该有)")
    
    # 检查7月31日数据（如果存在）
    target_date = date(2025, 7, 31)
    july_31_data = data[data['DateTime'].dt.date == target_date]
    
    if len(july_31_data) > 0:
        print(f"   📅 7月31日数据: {len(july_31_data)} 条")
        print(f"   时间范围: {july_31_data['DateTime'].min()} 到 {july_31_data['DateTime'].max()}")
        
        # 检查是否有23:00:00
        has_2300 = any(july_31_data['DateTime'].dt.time == time(23, 0))
        has_0900 = any(july_31_data['DateTime'].dt.time == time(9, 0))
        has_1500 = any(july_31_data['DateTime'].dt.time == time(15, 0))
        
        print(f"   23:00:00存在: {'✅' if has_2300 else '❌'}")
        print(f"   09:00:00存在: {'❌' if not has_0900 else '⚠️'}")
        print(f"   15:00:00存在: {'❌' if not has_1500 else '⚠️'}")

def main():
    """主函数"""
    print("🔧 螺纹钢数据AkShare格式修正工具")
    print("🎯 目标: 确保本地数据与AkShare格式完全一致")
    print("=" * 60)
    
    corrected_data = correct_rb_data_format()
    
    if corrected_data is not None:
        print("\n" + "=" * 60)
        print("✅ 数据格式修正完成!")
        print("🎯 现在本地数据与AkShare格式完全一致")
        print("💡 可用于真正的实盘一致性回测")

if __name__ == "__main__":
    main()
