#!/usr/bin/env python3
"""
处理螺纹钢1分钟K线数据
正确处理夜盘数据归属问题
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os

def process_rb_1min_data():
    """
    处理螺纹钢1分钟K线数据
    正确处理夜盘数据归属到下一交易日
    """
    print("🔄 开始处理螺纹钢1分钟K线数据...")
    
    # 1. 读取原始数据
    print("📂 读取rb.txt文件...")
    try:
        data = pd.read_csv('data/rb.txt', encoding='gbk')
        print(f"   ✅ 文件读取成功，总行数: {len(data):,}")
    except Exception as e:
        print(f"   ❌ 文件读取失败: {str(e)}")
        return
    
    # 2. 数据预处理
    print("🔧 数据预处理...")
    data['时间'] = pd.to_datetime(data['时间'])
    data = data.sort_values('时间').reset_index(drop=True)
    
    print(f"   📅 数据时间范围: {data['时间'].min()} 到 {data['时间'].max()}")
    
    # 3. 添加交易日字段（处理夜盘归属）
    print("📊 处理夜盘数据归属...")
    data['交易日'] = data['时间'].apply(get_trading_date)
    
    # 4. 筛选目标时间范围
    target_start = datetime(2022, 8, 1)
    target_end = datetime(2025, 7, 29, 23, 59, 59)
    
    print(f"🎯 筛选目标时间范围: {target_start.date()} 到 {target_end.date()}")
    
    # 筛选交易日在目标范围内的数据
    filtered_data = data[
        (data['交易日'] >= target_start.date()) & 
        (data['交易日'] <= target_end.date())
    ].copy()
    
    print(f"   ✅ 筛选后数据行数: {len(filtered_data):,}")
    
    if len(filtered_data) == 0:
        print("   ❌ 未找到目标时间范围的数据")
        return
    
    # 5. 数据质量检查
    print("🔍 数据质量检查...")
    check_data_quality(filtered_data)
    
    # 6. 重命名列以匹配现有系统
    print("🔄 重命名列...")
    filtered_data = filtered_data.rename(columns={
        '时间': 'DateTime',
        '开盘价': 'Open',
        '最高价': 'High', 
        '最低价': 'Low',
        '收盘价': 'Close',
        '成交量': 'Volume',
        '持仓量': 'OpenInterest',
        '结算价': 'Settlement'
    })
    
    # 7. 保存处理后的数据
    output_file = 'data/rb_1min_2022_2025.csv'
    print(f"💾 保存处理后的数据到: {output_file}")
    
    filtered_data.to_csv(output_file, index=False, encoding='utf-8')
    print(f"   ✅ 数据保存成功")
    
    # 8. 生成数据统计报告
    generate_data_report(filtered_data, output_file)
    
    return filtered_data

def get_trading_date(dt):
    """
    根据时间确定交易日
    夜盘（21:00-23:59）归属下一交易日
    """
    if dt.hour >= 21:  # 夜盘时间
        # 夜盘归属下一交易日
        next_day = dt.date() + timedelta(days=1)
        # 跳过周末
        while next_day.weekday() >= 5:  # 5=周六, 6=周日
            next_day += timedelta(days=1)
        return next_day
    elif dt.hour < 3:  # 凌晨时间（可能是夜盘延续）
        # 凌晨时间也归属当日
        current_day = dt.date()
        # 如果是周末，调整到下周一
        while current_day.weekday() >= 5:
            current_day += timedelta(days=1)
        return current_day
    else:  # 日盘时间
        current_day = dt.date()
        # 跳过周末
        while current_day.weekday() >= 5:
            current_day += timedelta(days=1)
        return current_day

def check_data_quality(data):
    """检查数据质量"""
    print("   📊 数据质量统计:")
    print(f"      总记录数: {len(data):,}")
    print(f"      时间范围: {data['时间'].min()} 到 {data['时间'].max()}")
    print(f"      交易日范围: {data['交易日'].min()} 到 {data['交易日'].max()}")
    print(f"      交易日数量: {data['交易日'].nunique()} 天")
    
    # 检查缺失值
    missing_data = data.isnull().sum()
    if missing_data.sum() > 0:
        print("   ⚠️ 发现缺失值:")
        for col, count in missing_data.items():
            if count > 0:
                print(f"      {col}: {count} 个")
    else:
        print("   ✅ 无缺失值")
    
    # 检查价格合理性
    price_cols = ['开盘价', '最高价', '最低价', '收盘价']
    for col in price_cols:
        if col in data.columns:
            min_price = data[col].min()
            max_price = data[col].max()
            print(f"   {col}: {min_price:.0f} - {max_price:.0f}")
    
    # 检查每日数据量
    daily_counts = data.groupby('交易日').size()
    print(f"   📊 每日平均数据量: {daily_counts.mean():.0f} 条")
    print(f"   📊 每日数据量范围: {daily_counts.min()} - {daily_counts.max()} 条")

def generate_data_report(data, output_file):
    """生成数据处理报告"""
    print("\n📋 生成数据处理报告...")
    
    report = [
        "# 螺纹钢1分钟K线数据处理报告",
        f"## 处理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
        "",
        "## 数据基本信息",
        f"- **源文件**: data/rb.txt",
        f"- **输出文件**: {output_file}",
        f"- **数据行数**: {len(data):,} 条",
        f"- **时间范围**: {data['DateTime'].min()} 到 {data['DateTime'].max()}",
        f"- **交易日范围**: {data['交易日'].min()} 到 {data['交易日'].max()}",
        f"- **交易日数量**: {data['交易日'].nunique()} 天",
        "",
        "## 夜盘处理规则",
        "- **夜盘时间**: 21:00-23:59",
        "- **归属规则**: 夜盘数据归属下一交易日",
        "- **周末处理**: 自动跳过周末，归属下周一",
        "",
        "## 数据字段",
        "- **DateTime**: 时间戳（精确到分钟）",
        "- **Open**: 开盘价",
        "- **High**: 最高价", 
        "- **Low**: 最低价",
        "- **Close**: 收盘价",
        "- **Volume**: 成交量",
        "- **OpenInterest**: 持仓量",
        "- **Settlement**: 结算价",
        "- **交易日**: 所属交易日",
        "",
        "## 数据统计",
    ]
    
    # 添加统计信息
    daily_counts = data.groupby('交易日').size()
    price_stats = data[['Open', 'High', 'Low', 'Close']].describe()
    
    report.extend([
        f"- **每日平均数据量**: {daily_counts.mean():.0f} 条",
        f"- **每日数据量范围**: {daily_counts.min()} - {daily_counts.max()} 条",
        f"- **价格范围**: {data['Low'].min():.0f} - {data['High'].max():.0f} 元/吨",
        f"- **平均收盘价**: {data['Close'].mean():.0f} 元/吨",
        "",
        "## 使用说明",
        "1. 数据已按时间排序",
        "2. 夜盘数据已正确归属到对应交易日",
        "3. 可直接用于1分钟级别的回测",
        "4. 与现有日K数据时间范围一致",
        "",
        "✅ **数据处理完成，可用于实盘一致性回测**"
    ])
    
    # 保存报告
    report_file = 'data/rb_1min_processing_report.md'
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write('\n'.join(report))
    
    print(f"   💾 报告已保存: {report_file}")

def main():
    """主函数"""
    print("🚀 螺纹钢1分钟K线数据处理工具")
    print("🎯 目标: 提取2022-08-01到2025-07-29的数据，正确处理夜盘归属")
    print("=" * 60)
    
    try:
        processed_data = process_rb_1min_data()
        
        if processed_data is not None:
            print("\n" + "=" * 60)
            print("✅ 数据处理完成!")
            print("🎯 现在可以基于1分钟数据进行真正的实盘一致性回测")
            
            # 显示2022-08-01的开盘价进行验证
            first_day_data = processed_data[processed_data['交易日'] == datetime(2022, 8, 1).date()]
            if len(first_day_data) > 0:
                first_price = first_day_data.iloc[0]['Open']
                print(f"🔍 验证: 2022-08-01开盘价 = {first_price:.0f} 元/吨")
        
    except Exception as e:
        print(f"❌ 处理失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
