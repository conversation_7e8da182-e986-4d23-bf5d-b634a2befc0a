#!/usr/bin/env python3
"""
测试盘中实时交易功能
避免未来函数，基于历史数据实时决策
"""

import sys
sys.path.append('src')

import pandas as pd
from datetime import datetime
from ai_realtime_strategy import AIRealtimeStrategy

def test_intraday_trading():
    """测试盘中实时交易功能"""
    print("⚡ 测试盘中实时交易功能")
    print("=" * 40)
    print("🎯 特点: 无需等待收盘，基于实时价格即时决策")
    print("🚫 严格避免未来函数，确保实盘可用")
    print()
    
    try:
        # 1. 初始化AI策略
        print("🤖 初始化AI策略...")
        strategy = AIRealtimeStrategy()
        
        # 2. 加载历史数据
        print("📊 加载历史数据...")
        historical_data = pd.read_csv('data/real_rebar_20250729.csv')
        historical_data['Date'] = pd.to_datetime(historical_data['Date'])
        historical_data = historical_data.sort_values('Date').reset_index(drop=True)
        
        print(f"   ✅ 历史数据: {len(historical_data)} 条记录")
        print(f"   📅 数据范围: {historical_data['Date'].min().strftime('%Y-%m-%d')} 至 {historical_data['Date'].max().strftime('%Y-%m-%d')}")
        
        # 3. 初始化交易日
        print("\n📊 初始化交易日...")
        if not strategy.initialize_trading_day(historical_data):
            print("❌ 交易日初始化失败")
            return
        
        print("✅ 交易日初始化完成")
        print(f"   📈 昨日MA30: {strategy.previous_ma30:.2f}")
        print(f"   💰 昨日收盘: {strategy.last_price:.2f}")
        
        # 4. 开始盘中实时交易测试
        print("\n🚀 盘中实时交易系统已启动")
        print("💡 输入价格获取实时交易信号")
        print("💡 输入'demo'运行演示")
        print("💡 输入'q'退出")
        print("=" * 40)
        
        while True:
            try:
                user_input = input("\n请输入当前价格 (或'demo'/'q'): ").strip()
                
                if user_input.lower() == 'q':
                    print("👋 退出盘中交易测试")
                    break
                
                elif user_input.lower() == 'demo':
                    print("🎬 运行演示...")
                    run_demo(strategy)
                    continue
                
                # 解析价格
                current_price = float(user_input)
                
                # 获取实时交易信号
                signal = strategy.get_intraday_trading_signal(current_price)
                
                # 显示信号
                display_signal(signal)
                
            except ValueError:
                print("❌ 请输入有效的价格数字")
            except KeyboardInterrupt:
                print("\n👋 用户中断，退出系统")
                break
            except Exception as e:
                print(f"❌ 处理失败: {str(e)}")
    
    except Exception as e:
        print(f"❌ 系统初始化失败: {str(e)}")

def run_demo(strategy):
    """运行演示"""
    print("\n🎬 盘中实时交易演示")
    print("-" * 30)
    
    # 模拟一系列价格变化
    demo_prices = [
        (3200, "开盘价"),
        (3210, "小幅上涨"),
        (3220, "继续上涨"),
        (3240, "突破MA30?"),
        (3250, "确认突破"),
        (3230, "回调"),
        (3180, "下跌"),
        (3160, "跌破MA30?"),
        (3150, "确认跌破"),
    ]
    
    for price, description in demo_prices:
        print(f"\n📊 {description}: {price}")
        signal = strategy.get_intraday_trading_signal(price)
        display_signal(signal, compact=True)
        
        if signal['action'] != 'hold':
            print(f"   🚨 交易信号: {signal['action']} @ {price}")
        
        input("   按回车继续...")

def display_signal(signal, compact=False):
    """显示交易信号"""
    if compact:
        print(f"   🎯 {signal['action']} | MA30:{signal['ma30']:.0f} | {signal['reason']}")
    else:
        print(f"\n📊 实时交易信号:")
        print(f"   ⏰ 时间: {signal['timestamp'].strftime('%H:%M:%S')}")
        print(f"   💰 价格: {signal['price']:.0f}")
        print(f"   📈 MA30: {signal['ma30']:.0f}")
        print(f"   🎯 动作: {signal['action']}")
        print(f"   📝 原因: {signal['reason']}")
        print(f"   🎲 置信度: {signal['confidence']:.1%}")
        
        if signal['action'] != 'hold':
            print(f"\n🚨 交易信号触发！")
            print(f"   💡 建议执行: {signal['action']} @ {signal['price']:.0f}")

def main():
    """主函数"""
    print("🧪 盘中实时交易功能测试")
    print("=" * 50)
    print("🎯 目标: 验证无未来函数的实时决策能力")
    print("⚡ 特点: 基于昨日MA30 + 实时价格计算")
    print()
    
    test_intraday_trading()

if __name__ == "__main__":
    main()
