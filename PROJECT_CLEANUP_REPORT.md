# 项目结构整理报告
## 整理时间: 2025-08-03 12:16:10

## 整理后的项目结构

### 核心文件
- `main.py` - 主程序入口
- `run_backtest.py` - 回测引擎
- `live_trading_main.py` - 实盘交易主程序
- `start_live_trading.py` - 实盘交易启动脚本

### 核心模块 (src/)
- `ai_realtime_strategy.py` - 核心AI策略模块
- `akshare_data_provider.py` - 数据获取模块

### 数据文件 (data/)
- `real_rebar_20250729.csv` - 历史日K数据
- `rb.txt` - 1分钟K线原始数据

### 模型文件 (models/)
- `breakout_models_20250729.pkl` - 假突破预测模型
- `stop_models_20250729.pkl` - 止损止盈决策模型

### 结果文件 (results/)
- 各种回测报告和交易计划

### 文档 (docs/)
- 项目文档和使用指南

### 工具 (tools/)
- `analysis/` - 分析工具
- `data_processing/` - 数据处理工具

### 归档 (archive/)
- `temp_files/` - 临时文件
- `test_files/` - 测试文件
- `old_strategies/` - 旧版策略文件

### 备份 (backup/)
- 模型和配置备份

## 清理内容

### 移动到归档的文件
- 临时数据处理脚本
- 测试和验证脚本
- 重复的策略文件

### 保留的核心文件
- 主要业务逻辑文件
- 配置和文档文件
- 数据和模型文件

## 使用说明

### 运行回测
```bash
python run_backtest.py
```

### 启动实盘交易
```bash
python start_live_trading.py
```

### 分析工具
```bash
python tools/analysis/analyze_yearly_returns.py
```

✅ **项目结构已优化，代码更加清晰和专业**